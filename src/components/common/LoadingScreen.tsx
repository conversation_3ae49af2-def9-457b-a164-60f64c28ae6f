import React from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

interface LoadingScreenProps {
  message?: string;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({ 
  message = 'Yükleniyor...' 
}) => {
  return (
    <LinearGradient
      colors={['#667eea', '#764ba2']}
      className="flex-1 justify-center items-center"
    >
      <View className="bg-white/90 backdrop-blur-sm rounded-3xl p-8 items-center shadow-2xl">
        <View className="mb-6">
          <Text className="text-4xl font-bold text-gray-800 mb-2">
            📚 LinguaRead
          </Text>
          <Text className="text-gray-600 text-center">
            Dil öğrenme yolculuğunuz
          </Text>
        </View>
        
        <ActivityIndicator size="large" color="#667eea" className="mb-4" />
        
        <Text className="text-gray-700 font-medium">
          {message}
        </Text>
        
        <View className="flex-row mt-4 space-x-2">
          <View className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
          <View className="w-2 h-2 bg-blue-500 rounded-full animate-pulse delay-100" />
          <View className="w-2 h-2 bg-blue-600 rounded-full animate-pulse delay-200" />
        </View>
      </View>
    </LinearGradient>
  );
};

export default LoadingScreen;
