import React, { useEffect, useState } from "react";
import { NavigationContainer } from "@react-navigation/native";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { onAuthStateChanged, User } from "firebase/auth";
import { auth } from "../services/firebase";
import { RootStackParamList } from "../types/navigation";
import AuthNavigator from "./AuthNavigator";
import MainNavigator from "./MainNavigator";
import { View, Text, ActivityIndicator, StyleSheet } from "react-native";
import { LinearGradient } from "expo-linear-gradient";

const Stack = createNativeStackNavigator<RootStackParamList>();

const AppNavigator: React.FC = () => {
    const [user, setUser] = useState<User | null>(null);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        const unsubscribe = onAuthStateChanged(auth, (user) => {
            setUser(user);
            setIsLoading(false);
        });

        return unsubscribe;
    }, []);

    if (isLoading) {
        return (
            <LinearGradient colors={["#667eea", "#764ba2"]} style={styles.container}>
                <View style={styles.loadingContainer}>
                    <View style={styles.logoContainer}>
                        <Text style={styles.logoIcon}>📚</Text>
                        <Text style={styles.logoText}>LinguaRead</Text>
                        <Text style={styles.logoSubtext}>Dil öğrenme yolculuğunuz</Text>
                    </View>
                    <ActivityIndicator size="large" color="white" style={styles.spinner} />
                    <Text style={styles.loadingText}>Kimlik doğrulanıyor...</Text>
                </View>
            </LinearGradient>
        );
    }

    return (
        <NavigationContainer>
            <Stack.Navigator screenOptions={{ headerShown: false }}>
                {user ? (
                    <Stack.Screen name="Main" component={MainNavigator} />
                ) : (
                    <Stack.Screen name="Auth" component={AuthNavigator} />
                )}
            </Stack.Navigator>
        </NavigationContainer>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        paddingHorizontal: 24,
    },
    logoContainer: {
        alignItems: "center",
        marginBottom: 48,
    },
    logoIcon: {
        fontSize: 80,
        marginBottom: 16,
    },
    logoText: {
        fontSize: 32,
        fontWeight: "bold",
        color: "white",
        marginBottom: 8,
    },
    logoSubtext: {
        fontSize: 16,
        color: "rgba(255, 255, 255, 0.8)",
        textAlign: "center",
    },
    spinner: {
        marginBottom: 16,
    },
    loadingText: {
        color: "white",
        fontSize: 16,
        fontWeight: "500",
    },
});

export default AppNavigator;
