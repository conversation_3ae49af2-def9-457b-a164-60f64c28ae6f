import React, { useState } from "react";
import { View, Text, TouchableOpacity, ScrollView } from "react-native";

const QuizScreen: React.FC = () => {
    const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
    const [currentQuestion, setCurrentQuestion] = useState(0);

    const sampleQuiz = {
        question: "What does 'serendipity' mean?",
        options: ["Tesadüf, şans eseri kar<PERSON>", "<PERSON><PERSON><PERSON><PERSON>ü, keder", "Hızlı hareket", "Yüksek ses"],
        correctAnswer: 0,
    };

    return (
        <ScrollView className="flex-1 bg-white">
            <View className="px-6 pt-12 pb-32">
                <Text className="text-2xl font-bold text-gray-900 mb-6">Quiz 🧠</Text>

                {/* Quiz Stats */}
                <View className="bg-green-50 rounded-xl p-6 mb-6">
                    <Text className="text-lg font-semibold text-green-900 mb-4">
                        Quiz İstatistikleri
                    </Text>
                    <View className="flex-row justify-between">
                        <View className="items-center">
                            <Text className="text-2xl font-bold text-green-600">15</Text>
                            <Text className="text-green-700 text-sm">Tamamlanan</Text>
                        </View>
                        <View className="items-center">
                            <Text className="text-2xl font-bold text-green-600">87%</Text>
                            <Text className="text-green-700 text-sm">Başarı Oranı</Text>
                        </View>
                        <View className="items-center">
                            <Text className="text-2xl font-bold text-green-600">5</Text>
                            <Text className="text-green-700 text-sm">Günlük Hedef</Text>
                        </View>
                    </View>
                </View>

                {/* Current Quiz */}
                <View className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm mb-6">
                    <View className="flex-row justify-between items-center mb-4">
                        <Text className="text-sm text-gray-600">Soru 1/10</Text>
                        <View className="bg-blue-100 px-3 py-1 rounded-full">
                            <Text className="text-blue-700 text-sm font-semibold">
                                Kelime Anlamı
                            </Text>
                        </View>
                    </View>

                    <Text className="text-lg font-semibold text-gray-900 mb-6">
                        {sampleQuiz.question}
                    </Text>

                    <View className="space-y-3">
                        {sampleQuiz.options.map((option, index) => (
                            <TouchableOpacity
                                key={index}
                                className={`p-4 rounded-lg border-2 ${
                                    selectedAnswer === index
                                        ? "border-blue-500 bg-blue-50"
                                        : "border-gray-200 bg-gray-50"
                                }`}
                                onPress={() => setSelectedAnswer(index)}
                            >
                                <Text
                                    className={`${
                                        selectedAnswer === index ? "text-blue-700" : "text-gray-700"
                                    }`}
                                >
                                    {String.fromCharCode(65 + index)}. {option}
                                </Text>
                            </TouchableOpacity>
                        ))}
                    </View>

                    <TouchableOpacity
                        className={`mt-6 py-3 rounded-lg ${
                            selectedAnswer !== null ? "bg-blue-500" : "bg-gray-300"
                        }`}
                        disabled={selectedAnswer === null}
                    >
                        <Text className="text-white text-center font-semibold">Cevapla</Text>
                    </TouchableOpacity>
                </View>

                {/* Quick Quiz Options */}
                <View>
                    <Text className="text-lg font-semibold text-gray-900 mb-4">Hızlı Quiz</Text>

                    <View className="space-y-3">
                        <TouchableOpacity className="bg-blue-500 rounded-xl p-4 flex-row items-center justify-between">
                            <View>
                                <Text className="text-white font-semibold text-lg">
                                    Kelime Anlamları
                                </Text>
                                <Text className="text-blue-100">10 soruluk hızlı quiz</Text>
                            </View>
                            <Text className="text-white text-2xl">📚</Text>
                        </TouchableOpacity>

                        <TouchableOpacity className="bg-green-500 rounded-xl p-4 flex-row items-center justify-between">
                            <View>
                                <Text className="text-white font-semibold text-lg">
                                    Cümle Tamamlama
                                </Text>
                                <Text className="text-green-100">Bağlamda kelime kullanımı</Text>
                            </View>
                            <Text className="text-white text-2xl">✏️</Text>
                        </TouchableOpacity>

                        <TouchableOpacity className="bg-purple-500 rounded-xl p-4 flex-row items-center justify-between">
                            <View>
                                <Text className="text-white font-semibold text-lg">
                                    Karışık Quiz
                                </Text>
                                <Text className="text-purple-100">Tüm kelimelerden rastgele</Text>
                            </View>
                            <Text className="text-white text-2xl">🎲</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </ScrollView>
    );
};

export default QuizScreen;
