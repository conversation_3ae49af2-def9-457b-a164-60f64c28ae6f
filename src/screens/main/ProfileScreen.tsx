import React from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { authService } from '../../services/auth';

const ProfileScreen: React.FC = () => {
  const handleLogout = async () => {
    try {
      await authService.signOut();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <ScrollView className="flex-1 bg-white">
      <View className="px-6 pt-12 pb-6">
        <Text className="text-2xl font-bold text-gray-900 mb-6">
          Profil 👤
        </Text>

        {/* User Info */}
        <View className="bg-blue-50 rounded-xl p-6 mb-6">
          <View className="items-center mb-4">
            <View className="w-20 h-20 bg-blue-500 rounded-full items-center justify-center mb-3">
              <Text className="text-white text-2xl font-bold">U</Text>
            </View>
            <Text className="text-xl font-semibold text-gray-900">
              <PERSON><PERSON><PERSON><PERSON><PERSON> Adı
            </Text>
            <Text className="text-gray-600">
              <EMAIL>
            </Text>
          </View>
          
          <View className="flex-row justify-between bg-white rounded-lg p-4">
            <View className="items-center">
              <Text className="text-lg font-bold text-blue-600">127</Text>
              <Text className="text-gray-600 text-sm">Kelime</Text>
            </View>
            <View className="items-center">
              <Text className="text-lg font-bold text-blue-600">15</Text>
              <Text className="text-gray-600 text-sm">Quiz</Text>
            </View>
            <View className="items-center">
              <Text className="text-lg font-bold text-blue-600">7</Text>
              <Text className="text-gray-600 text-sm">Gün Streak</Text>
            </View>
          </View>
        </View>

        {/* Settings */}
        <View className="space-y-3 mb-6">
          <Text className="text-lg font-semibold text-gray-900 mb-2">
            Ayarlar
          </Text>
          
          <TouchableOpacity className="bg-white rounded-xl p-4 border border-gray-200 flex-row items-center justify-between">
            <View className="flex-row items-center">
              <Text className="text-lg mr-3">🔔</Text>
              <Text className="text-gray-900 font-medium">Bildirimler</Text>
            </View>
            <Text className="text-gray-400">›</Text>
          </TouchableOpacity>

          <TouchableOpacity className="bg-white rounded-xl p-4 border border-gray-200 flex-row items-center justify-between">
            <View className="flex-row items-center">
              <Text className="text-lg mr-3">🌍</Text>
              <Text className="text-gray-900 font-medium">Dil Ayarları</Text>
            </View>
            <Text className="text-gray-400">›</Text>
          </TouchableOpacity>

          <TouchableOpacity className="bg-white rounded-xl p-4 border border-gray-200 flex-row items-center justify-between">
            <View className="flex-row items-center">
              <Text className="text-lg mr-3">📊</Text>
              <Text className="text-gray-900 font-medium">İstatistikler</Text>
            </View>
            <Text className="text-gray-400">›</Text>
          </TouchableOpacity>

          <TouchableOpacity className="bg-white rounded-xl p-4 border border-gray-200 flex-row items-center justify-between">
            <View className="flex-row items-center">
              <Text className="text-lg mr-3">💾</Text>
              <Text className="text-gray-900 font-medium">Veri Yedekleme</Text>
            </View>
            <Text className="text-gray-400">›</Text>
          </TouchableOpacity>

          <TouchableOpacity className="bg-white rounded-xl p-4 border border-gray-200 flex-row items-center justify-between">
            <View className="flex-row items-center">
              <Text className="text-lg mr-3">❓</Text>
              <Text className="text-gray-900 font-medium">Yardım & Destek</Text>
            </View>
            <Text className="text-gray-400">›</Text>
          </TouchableOpacity>
        </View>

        {/* Learning Goals */}
        <View className="bg-green-50 rounded-xl p-6 mb-6">
          <Text className="text-lg font-semibold text-green-900 mb-4">
            Öğrenme Hedefleri
          </Text>
          <View className="space-y-3">
            <View className="flex-row justify-between items-center">
              <Text className="text-green-700">Günlük kelime hedefi</Text>
              <Text className="text-green-900 font-semibold">10 kelime</Text>
            </View>
            <View className="flex-row justify-between items-center">
              <Text className="text-green-700">Haftalık quiz hedefi</Text>
              <Text className="text-green-900 font-semibold">5 quiz</Text>
            </View>
            <View className="flex-row justify-between items-center">
              <Text className="text-green-700">Okuma süresi</Text>
              <Text className="text-green-900 font-semibold">30 dakika</Text>
            </View>
          </View>
        </View>

        {/* Logout Button */}
        <TouchableOpacity
          className="bg-red-500 rounded-xl p-4"
          onPress={handleLogout}
        >
          <Text className="text-white font-semibold text-center text-lg">
            Çıkış Yap
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

export default ProfileScreen;
