# LinguaRead 📚

LinguaRead, PDF okuyarak dil öğrenmenizi destekleyen modern bir React Native uygulamasıdır. Tek kod tabanı ile iOS, Android ve Web platformlarında çalışır.

## 🚀 Özellikler

- **📱 Cross-Platform**: iOS, Android ve Web desteği
- **🔥 Firebase Entegrasyonu**: Auth, Firestore, Storage
- **🎨 Modern UI**: NativeWind (Tailwind CSS) ile responsive tasarım
- **🌐 Çeviri Desteği**: OpenAI ve Google Translate API entegrasyonu
- **📖 PDF Okuma**: Doküman yükleme ve okuma
- **📝 Kelime Listesi**: Öğrenilen kelimeleri kaydetme ve yönetme
- **🧠 Quiz Sistemi**: Öğrenilen kelimeleri test etme
- **📊 İlerleme Takibi**: Detaylı istatistikler ve analiz

## 🛠️ Teknolojiler

- **React Native** (Expo)
- **TypeScript**
- **React Navigation** (Stack + Tab)
- **NativeWind** (Tailwind CSS)
- **Firebase** (Auth, Firestore, Storage)
- **React Native Web**

## 📋 Gereksinimler

- Node.js 18+
- npm veya yarn
- Expo CLI
- Firebase projesi
- OpenAI API anahtarı (opsiyonel)
- Google Translate API anahtarı (opsiyonel)

## ⚡ Hızlı Başlangıç

### 1. Projeyi Klonlayın

```bash
git clone <repository-url>
cd LinguaRead
```

### 2. Bağımlılıkları Yükleyin

```bash
npm install
```

### 3. Ortam Değişkenlerini Ayarlayın

`.env` dosyasını düzenleyin ve Firebase yapılandırmanızı ekleyin:

```env
# Firebase Configuration
EXPO_PUBLIC_FIREBASE_API_KEY=your_api_key_here
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
EXPO_PUBLIC_FIREBASE_APP_ID=your_app_id

# API Keys (Opsiyonel)
EXPO_PUBLIC_OPENAI_API_KEY=your_openai_api_key_here
EXPO_PUBLIC_GOOGLE_TRANSLATE_API_KEY=your_google_translate_api_key_here
```

### 4. Uygulamayı Çalıştırın

```bash
# Geliştirme sunucusunu başlat
npm start

# Belirli platform için
npm run ios      # iOS
npm run android  # Android
npm run web      # Web
```

## 🔧 Firebase Kurulumu

### 1. Firebase Projesi Oluşturun

1. [Firebase Console](https://console.firebase.google.com/)'a gidin
2. Yeni proje oluşturun
3. Authentication, Firestore ve Storage'ı etkinleştirin

### 2. Web Uygulaması Ekleyin

1. Firebase projenizde "Web" uygulaması ekleyin
2. Yapılandırma bilgilerini `.env` dosyasına ekleyin

### 3. Authentication Ayarları

1. Authentication > Sign-in method
2. Email/Password'ü etkinleştirin
3. Google Sign-in'i etkinleştirin (opsiyonel)

### 4. Firestore Kuralları

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /userWords/{document} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
    
    match /quizResults/{document} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
    
    match /readingSessions/{document} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
  }
}
```

## 📁 Proje Yapısı

```
src/
├── components/          # Yeniden kullanılabilir bileşenler
├── screens/            # Ekran bileşenleri
│   ├── auth/           # Giriş/kayıt ekranları
│   └── main/           # Ana uygulama ekranları
├── navigation/         # Navigation yapılandırması
├── services/           # API ve Firebase servisleri
├── hooks/              # Custom React hooks
├── utils/              # Yardımcı fonksiyonlar
├── types/              # TypeScript tip tanımları
└── theme/              # Tema ve stil ayarları
```

## 🎯 Kullanım

### Kelime Çevirisi

```typescript
import { translateWord } from './src/services/translation';

const result = await translateWord('serendipity');
console.log(result.meaning); // "tesadüf, şans eseri karşılaşma"
```

### Firestore İşlemleri

```typescript
import { firestoreService } from './src/services/firestore';

// Kelime ekleme
await firestoreService.addUserWord(userId, translationResult);

// Kullanıcı kelimelerini getirme
const words = await firestoreService.getUserWords(userId);
```

## 🧪 Test

```bash
# Unit testleri çalıştır
npm test

# E2E testleri çalıştır
npm run test:e2e
```

## 📱 Build ve Deploy

### Expo Development Build

```bash
# Development build oluştur
eas build --profile development

# Production build oluştur
eas build --profile production
```

### Web Deploy

```bash
# Web için build
npm run build:web

# Static dosyaları deploy et
npm run deploy:web
```

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit edin (`git commit -m 'Add amazing feature'`)
4. Push edin (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

## 🆘 Destek

Sorunlarınız için:
- GitHub Issues
- Email: <EMAIL>
- Discord: [LinguaRead Community](https://discord.gg/linguaread)

## 🔄 Güncellemeler

- v1.0.0 - İlk sürüm
- Gelecek güncellemeler için [CHANGELOG.md](CHANGELOG.md) dosyasını takip edin

---

**LinguaRead** ile dil öğrenme yolculuğunuza başlayın! 🚀
