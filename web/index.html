<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="utf-8" />
    <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    
    <!-- SEO Meta Tags -->
    <title>LinguaRead - Dil Öğrenme Uygulaması</title>
    <meta name="description" content="PDF okuyarak İngilizce öğrenin. Kelime çevirisi, quiz ve daha fazlası." />
    <meta name="keywords" content="dil öğrenme, İngilizce, PDF okuma, kelime öğrenme, quiz" />
    <meta name="author" content="LinguaRead" />
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="LinguaRead - Dil Öğrenme Uygulaması" />
    <meta property="og:description" content="PDF okuyarak İngilizce öğrenin. Ke<PERSON><PERSON> çeviri<PERSON>, quiz ve daha fazlası." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://linguaread.app" />
    <meta property="og:image" content="/assets/icon.png" />
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="LinguaRead - Dil Öğrenme Uygulaması" />
    <meta name="twitter:description" content="PDF okuyarak İngilizce öğrenin. Kelime çevirisi, quiz ve daha fazlası." />
    <meta name="twitter:image" content="/assets/icon.png" />
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="/assets/favicon.png" />
    <link rel="apple-touch-icon" href="/assets/icon.png" />
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        },
                        secondary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                        }
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    
    <style>
        body {
            font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #ffffff;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        #root {
            height: 100vh;
            width: 100vw;
            display: flex;
            flex-direction: column;
        }
        
        /* Loading spinner */
        .loading-spinner {
            border: 4px solid #f3f4f6;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            body {
                font-size: 14px;
            }
        }
        
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
    </style>
</head>
<body>
    <noscript>
        <div style="display: flex; justify-content: center; align-items: center; height: 100vh; flex-direction: column; font-family: Inter, sans-serif;">
            <h1 style="color: #1e40af; margin-bottom: 16px;">LinguaRead</h1>
            <p style="color: #64748b; text-align: center; max-width: 400px;">
                Bu uygulama JavaScript gerektirir. Lütfen tarayıcınızda JavaScript'i etkinleştirin.
            </p>
        </div>
    </noscript>
    
    <div id="root">
        <!-- Loading screen -->
        <div id="loading-screen" style="display: flex; justify-content: center; align-items: center; height: 100vh; flex-direction: column;">
            <div class="loading-spinner"></div>
            <p style="margin-top: 16px; color: #64748b; font-weight: 500;">LinguaRead yükleniyor...</p>
        </div>
    </div>
    
    <script>
        // Hide loading screen when app loads
        window.addEventListener('load', function() {
            setTimeout(function() {
                const loadingScreen = document.getElementById('loading-screen');
                if (loadingScreen) {
                    loadingScreen.style.display = 'none';
                }
            }, 1000);
        });
        
        // Service Worker registration for PWA
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    </script>
</body>
</html>
