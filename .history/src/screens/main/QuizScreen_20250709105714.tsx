import React, { useState } from "react";
import { View, Text, TouchableOpacity, ScrollView, StyleSheet } from "react-native";
import { LinearGradient } from "expo-linear-gradient";

const QuizScreen: React.FC = () => {
    const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
    const [currentQuestion, setCurrentQuestion] = useState(0);

    const sampleQuiz = {
        question: "What does 'serendipity' mean?",
        options: ["Tesadüf, şans eseri karşılaşma", "<PERSON><PERSON><PERSON><PERSON><PERSON>, keder", "<PERSON><PERSON>z<PERSON>ı hareket", "<PERSON>üksek ses"],
        correctAnswer: 0,
    };

    return (
        <LinearGradient colors={["#f8fafc", "#e2e8f0"]} style={styles.container}>
            <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                <View style={styles.content}>
                    {/* Header */}
                    <View style={styles.header}>
                        <Text style={styles.title}>Quiz Merkezi 🧠</Text>
                        <Text style={styles.subtitle}>
                            Bilginizi test edin ve kelime da<PERSON>ğınızı geliştirin
                        </Text>
                    </View>

                    {/* Quiz Stats */}
                    <LinearGradient
                        colors={["#10b981", "#059669"]}
                        style={styles.statsCard}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                    >
                        <Text style={styles.statsTitle}>📊 Quiz İstatistikleri</Text>
                        <View style={styles.statsContainer}>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>15</Text>
                                <Text style={styles.statLabel}>Tamamlanan</Text>
                            </View>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>87%</Text>
                                <Text style={styles.statLabel}>Başarı Oranı</Text>
                            </View>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>5</Text>
                                <Text style={styles.statLabel}>Günlük Hedef</Text>
                            </View>
                        </View>
                    </LinearGradient>

                {/* Current Quiz */}
                <View className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm mb-6">
                    <View className="flex-row justify-between items-center mb-4">
                        <Text className="text-sm text-gray-600">Soru 1/10</Text>
                        <View className="bg-blue-100 px-3 py-1 rounded-full">
                            <Text className="text-blue-700 text-sm font-semibold">
                                Kelime Anlamı
                            </Text>
                        </View>
                    </View>

                    <Text className="text-lg font-semibold text-gray-900 mb-6">
                        {sampleQuiz.question}
                    </Text>

                    <View className="space-y-3">
                        {sampleQuiz.options.map((option, index) => (
                            <TouchableOpacity
                                key={index}
                                className={`p-4 rounded-lg border-2 ${
                                    selectedAnswer === index
                                        ? "border-blue-500 bg-blue-50"
                                        : "border-gray-200 bg-gray-50"
                                }`}
                                onPress={() => setSelectedAnswer(index)}
                            >
                                <Text
                                    className={`${
                                        selectedAnswer === index ? "text-blue-700" : "text-gray-700"
                                    }`}
                                >
                                    {String.fromCharCode(65 + index)}. {option}
                                </Text>
                            </TouchableOpacity>
                        ))}
                    </View>

                    <TouchableOpacity
                        className={`mt-6 py-3 rounded-lg ${
                            selectedAnswer !== null ? "bg-blue-500" : "bg-gray-300"
                        }`}
                        disabled={selectedAnswer === null}
                    >
                        <Text className="text-white text-center font-semibold">Cevapla</Text>
                    </TouchableOpacity>
                </View>

                {/* Quick Quiz Options */}
                <View>
                    <Text className="text-lg font-semibold text-gray-900 mb-4">Hızlı Quiz</Text>

                    <View className="space-y-3">
                        <TouchableOpacity className="bg-blue-500 rounded-xl p-4 flex-row items-center justify-between">
                            <View>
                                <Text className="text-white font-semibold text-lg">
                                    Kelime Anlamları
                                </Text>
                                <Text className="text-blue-100">10 soruluk hızlı quiz</Text>
                            </View>
                            <Text className="text-white text-2xl">📚</Text>
                        </TouchableOpacity>

                        <TouchableOpacity className="bg-green-500 rounded-xl p-4 flex-row items-center justify-between">
                            <View>
                                <Text className="text-white font-semibold text-lg">
                                    Cümle Tamamlama
                                </Text>
                                <Text className="text-green-100">Bağlamda kelime kullanımı</Text>
                            </View>
                            <Text className="text-white text-2xl">✏️</Text>
                        </TouchableOpacity>

                        <TouchableOpacity className="bg-purple-500 rounded-xl p-4 flex-row items-center justify-between">
                            <View>
                                <Text className="text-white font-semibold text-lg">
                                    Karışık Quiz
                                </Text>
                                <Text className="text-purple-100">Tüm kelimelerden rastgele</Text>
                            </View>
                            <Text className="text-white text-2xl">🎲</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </ScrollView>
    );
};

export default QuizScreen;
