import React from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';

const ReadingScreen: React.FC = () => {
  return (
    <ScrollView className="flex-1 bg-white">
      <View className="px-6 pt-12 pb-6">
        <Text className="text-2xl font-bold text-gray-900 mb-6">
          Okuma 📖
        </Text>

        {/* Upload Section */}
        <View className="bg-blue-50 rounded-xl p-6 mb-6">
          <Text className="text-lg font-semibold text-blue-900 mb-2">
            PDF Yükle
          </Text>
          <Text className="text-blue-700 mb-4">
            Okumak istediğiniz PDF dosyasını yükleyin
          </Text>
          <TouchableOpacity className="bg-blue-500 rounded-lg py-3 px-6">
            <Text className="text-white font-semibold text-center">
              Do<PERSON>a <PERSON>ç
            </Text>
          </TouchableOpacity>
        </View>

        {/* Recent Documents */}
        <View>
          <Text className="text-lg font-semibold text-gray-900 mb-4">
            Son <PERSON>ım
          </Text>
          
          <View className="space-y-3">
            <View className="bg-gray-50 rounded-xl p-4 border border-gray-200">
              <Text className="font-semibold text-gray-900 mb-1">
                Örnek Doküman 1
              </Text>
              <Text className="text-gray-600 text-sm mb-2">
                Son okuma: 2 saat önce
              </Text>
              <View className="flex-row justify-between items-center">
                <Text className="text-blue-600 text-sm">%75 tamamlandı</Text>
                <TouchableOpacity className="bg-blue-500 px-4 py-2 rounded-lg">
                  <Text className="text-white text-sm font-semibold">
                    Devam Et
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            <View className="bg-gray-50 rounded-xl p-4 border border-gray-200">
              <Text className="font-semibold text-gray-900 mb-1">
                Örnek Doküman 2
              </Text>
              <Text className="text-gray-600 text-sm mb-2">
                Son okuma: 1 gün önce
              </Text>
              <View className="flex-row justify-between items-center">
                <Text className="text-blue-600 text-sm">%45 tamamlandı</Text>
                <TouchableOpacity className="bg-blue-500 px-4 py-2 rounded-lg">
                  <Text className="text-white text-sm font-semibold">
                    Devam Et
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>

        {/* Reading Tips */}
        <View className="mt-8 bg-green-50 rounded-xl p-6">
          <Text className="text-lg font-semibold text-green-900 mb-2">
            💡 Okuma İpuçları
          </Text>
          <Text className="text-green-700">
            • Bilmediğiniz kelimelere dokunarak anlamlarını öğrenin{'\n'}
            • Önemli kelimeleri kelime listenize ekleyin{'\n'}
            • Düzenli okuma alışkanlığı edinin
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};

export default ReadingScreen;
