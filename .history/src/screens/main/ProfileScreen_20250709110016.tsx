import React from "react";
import { View, Text, TouchableOpacity, ScrollView, StyleSheet } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { authService } from "../../services/auth";

const ProfileScreen: React.FC = () => {
    const handleLogout = async () => {
        try {
            await authService.signOut();
        } catch (error) {
            console.error("Logout error:", error);
        }
    };

    return (
        <LinearGradient colors={["#f8fafc", "#e2e8f0"]} style={styles.container}>
            <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                <View style={styles.content}>
                    {/* Header */}
                    <View style={styles.header}>
                        <Text style={styles.title}>Profilim 👤</Text>
                        <Text style={styles.subtitle}>
                            Hesap bilgileriniz ve öğrenme istatistikleriniz
                        </Text>
                    </View>

                    {/* User Info */}
                    <LinearGradient
                        colors={["#667eea", "#764ba2"]}
                        style={styles.userCard}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                    >
                        <View style={styles.userInfo}>
                            <View style={styles.avatarContainer}>
                                <LinearGradient
                                    colors={["#ffffff", "#f0f0f0"]}
                                    style={styles.avatar}
                                >
                                    <Text style={styles.avatarText}>U</Text>
                                </LinearGradient>
                            </View>
                            <Text style={styles.userName}>Kullanıcı Adı</Text>
                            <Text style={styles.userEmail}><EMAIL></Text>
                        </View>

                        <View style={styles.statsContainer}>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>127</Text>
                                <Text style={styles.statLabel}>Kelime</Text>
                            </View>
                            <View style={styles.statDivider} />
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>15</Text>
                                <Text style={styles.statLabel}>Quiz</Text>
                            </View>
                            <View style={styles.statDivider} />
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>7</Text>
                                <Text style={styles.statLabel}>Gün Streak</Text>
                            </View>
                        </View>
                    </LinearGradient>

                    {/* Settings */}
                    <View style={styles.section}>
                        <Text style={styles.sectionTitle}>⚙️ Ayarlar</Text>

                        <View style={styles.settingsContainer}>
                            <TouchableOpacity style={styles.settingItem}>
                                <View style={styles.settingContent}>
                                    <View style={styles.settingIcon}>
                                        <Text style={styles.settingEmoji}>🔔</Text>
                                    </View>
                                    <Text style={styles.settingText}>Bildirimler</Text>
                                </View>
                                <Text style={styles.settingArrow}>›</Text>
                            </TouchableOpacity>

                            <TouchableOpacity style={styles.settingItem}>
                                <View style={styles.settingContent}>
                                    <View style={styles.settingIcon}>
                                        <Text style={styles.settingEmoji}>🌍</Text>
                                    </View>
                                    <Text style={styles.settingText}>Dil Ayarları</Text>
                                </View>
                                <Text style={styles.settingArrow}>›</Text>
                            </TouchableOpacity>

                            <TouchableOpacity style={styles.settingItem}>
                                <View style={styles.settingContent}>
                                    <View style={styles.settingIcon}>
                                        <Text style={styles.settingEmoji}>📊</Text>
                                    </View>
                                    <Text style={styles.settingText}>İstatistikler</Text>
                                </View>
                                <Text style={styles.settingArrow}>›</Text>
                            </TouchableOpacity>

                            <TouchableOpacity style={styles.settingItem}>
                                <View style={styles.settingContent}>
                                    <View style={styles.settingIcon}>
                                        <Text style={styles.settingEmoji}>💾</Text>
                                    </View>
                                    <Text style={styles.settingText}>Veri Yedekleme</Text>
                                </View>
                                <Text style={styles.settingArrow}>›</Text>
                            </TouchableOpacity>

                            <TouchableOpacity style={styles.settingItem}>
                                <View style={styles.settingContent}>
                                    <View style={styles.settingIcon}>
                                        <Text style={styles.settingEmoji}>❓</Text>
                                    </View>
                                    <Text style={styles.settingText}>Yardım & Destek</Text>
                                </View>
                                <Text style={styles.settingArrow}>›</Text>
                            </TouchableOpacity>
                        </View>
                    </View>

                    {/* Learning Goals */}
                    <LinearGradient
                        colors={["#10b981", "#059669"]}
                        style={styles.goalsCard}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                    >
                        <Text style={styles.goalsTitle}>🎯 Öğrenme Hedefleri</Text>
                        <View style={styles.goalsContainer}>
                            <View style={styles.goalItem}>
                                <Text style={styles.goalLabel}>Günlük kelime hedefi</Text>
                                <Text style={styles.goalValue}>10 kelime</Text>
                            </View>
                            <View style={styles.goalDivider} />
                            <View style={styles.goalItem}>
                                <Text style={styles.goalLabel}>Haftalık quiz hedefi</Text>
                                <Text style={styles.goalValue}>5 quiz</Text>
                            </View>
                            <View style={styles.goalDivider} />
                            <View style={styles.goalItem}>
                                <Text style={styles.goalLabel}>Okuma süresi</Text>
                                <Text style={styles.goalValue}>30 dakika</Text>
                            </View>
                        </View>
                    </LinearGradient>

                    {/* Logout Button */}
                    <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
                        <LinearGradient
                            colors={["#ef4444", "#dc2626"]}
                            style={styles.logoutGradient}
                            start={{ x: 0, y: 0 }}
                            end={{ x: 1, y: 1 }}
                        >
                            <Text style={styles.logoutIcon}>🚪</Text>
                            <Text style={styles.logoutText}>Çıkış Yap</Text>
                        </LinearGradient>
                    </TouchableOpacity>
                </View>
            </ScrollView>
        </LinearGradient>
    );
};

export default ProfileScreen;
