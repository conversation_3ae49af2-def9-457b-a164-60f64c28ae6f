import React from "react";
import { View, Text, ScrollView, StyleSheet, TouchableOpacity } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useNavigation } from "@react-navigation/native";
import { authService } from "../../services/auth";

const HomeScreen: React.FC = () => {
    const navigation = useNavigation();

    const handleLogout = async () => {
        try {
            await authService.signOut();
        } catch (error) {
            console.error("Logout error:", error);
        }
    };

    return (
        <LinearGradient colors={["#f8fafc", "#e2e8f0"]} style={styles.container}>
            <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                <View style={styles.content}>
                    {/* Header */}
                    <View style={styles.header}>
                        <View style={styles.headerText}>
                            <Text style={styles.title}>Hoş Geldiniz! 👋</Text>
                            <Text style={styles.subtitle}>
                                Di<PERSON> <PERSON>ğrenme yolculuğunuza devam edin
                            </Text>
                        </View>
                        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
                            <Text style={styles.logoutButtonText}>🚪 Çıkış</Text>
                        </TouchableOpacity>
                    </View>

                    {/* Quick Stats */}
                    <LinearGradient
                        colors={["#667eea", "#764ba2"]}
                        style={styles.statsCard}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                    >
                        <Text style={styles.statsTitle}>📊 Günlük İlerleme</Text>
                        <View style={styles.statsContainer}>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>12</Text>
                                <Text style={styles.statLabel}>Yeni Kelime</Text>
                            </View>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>3</Text>
                                <Text style={styles.statLabel}>Tamamlanan Quiz</Text>
                            </View>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>25</Text>
                                <Text style={styles.statLabel}>Dakika Okuma</Text>
                            </View>
                        </View>
                    </LinearGradient>

                    {/* Quick Actions */}
                    <View style={styles.actionsSection}>
                        <Text style={styles.sectionTitle}>🚀 Hızlı Erişim</Text>

                        <View style={styles.actionsContainer}>
                            <TouchableOpacity
                                style={styles.actionCard}
                                onPress={() => navigation.navigate("Reading" as never)}
                            >
                                <LinearGradient
                                    colors={["#4facfe", "#00f2fe"]}
                                    style={styles.actionGradient}
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 1, y: 1 }}
                                >
                                    <View style={styles.actionContent}>
                                        <View style={styles.actionTextContainer}>
                                            <Text style={styles.actionTitle}>📖 Okumaya Başla</Text>
                                            <Text style={styles.actionSubtitle}>
                                                PDF dosyalarınızı yükleyin ve okuyun
                                            </Text>
                                        </View>
                                        <View style={styles.actionIcon}>
                                            <Text style={styles.actionEmoji}>📖</Text>
                                        </View>
                                    </View>
                                </LinearGradient>
                            </TouchableOpacity>

                            <TouchableOpacity
                                style={styles.actionCard}
                                onPress={() => navigation.navigate("Quiz" as never)}
                            >
                                <LinearGradient
                                    colors={["#43e97b", "#38f9d7"]}
                                    style={styles.actionGradient}
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 1, y: 1 }}
                                >
                                    <View style={styles.actionContent}>
                                        <View style={styles.actionTextContainer}>
                                            <Text style={styles.actionTitle}>🧠 Quiz Çöz</Text>
                                            <Text style={styles.actionSubtitle}>
                                                Öğrendiğiniz kelimeleri test edin
                                            </Text>
                                        </View>
                                        <View style={styles.actionIcon}>
                                            <Text style={styles.actionEmoji}>🧠</Text>
                                        </View>
                                    </View>
                                </LinearGradient>
                            </TouchableOpacity>

                            <TouchableOpacity
                                style={styles.actionCard}
                                onPress={() => navigation.navigate("WordList" as never)}
                            >
                                <LinearGradient
                                    colors={["#fa709a", "#fee140"]}
                                    style={styles.actionGradient}
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 1, y: 1 }}
                                >
                                    <View style={styles.actionContent}>
                                        <View style={styles.actionTextContainer}>
                                            <Text style={styles.actionTitle}>📝 Kelime Listem</Text>
                                            <Text style={styles.actionSubtitle}>
                                                Kaydettiğiniz kelimeleri görüntüleyin
                                            </Text>
                                        </View>
                                        <View style={styles.actionIcon}>
                                            <Text style={styles.actionEmoji}>📝</Text>
                                        </View>
                                    </View>
                                </LinearGradient>
                            </TouchableOpacity>
                        </View>
                    </View>

                    {/* Recent Activity */}
                    <View style={styles.activityCard}>
                        <Text style={styles.activityTitle}>📈 Son Aktiviteler</Text>
                        <View style={styles.activityContent}>
                            <Text style={styles.activityIcon}>📚</Text>
                            <Text style={styles.activityText}>Henüz aktivite bulunmuyor</Text>
                            <Text style={styles.activitySubtext}>
                                Okuma veya quiz yapmaya başlayın!
                            </Text>
                        </View>
                    </View>
            </View>
        </ScrollView>
    );
};

export default HomeScreen;
