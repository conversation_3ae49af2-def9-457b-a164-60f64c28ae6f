import React from "react";
import { View, Text, TouchableOpacity, ScrollView, StyleSheet } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { authService } from "../../services/auth";

const ProfileScreen: React.FC = () => {
    const handleLogout = async () => {
        try {
            await authService.signOut();
        } catch (error) {
            console.error("Logout error:", error);
        }
    };

    return (
        <LinearGradient colors={["#f8fafc", "#e2e8f0"]} style={styles.container}>
            <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                <View style={styles.content}>
                    {/* Header */}
                    <View style={styles.header}>
                        <Text style={styles.title}>Profilim 👤</Text>
                        <Text style={styles.subtitle}>
                            Hesap bilgileriniz ve öğrenme istatistikleriniz
                        </Text>
                    </View>

                    {/* User Info */}
                    <LinearGradient
                        colors={["#667eea", "#764ba2"]}
                        style={styles.userCard}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                    >
                        <View style={styles.userInfo}>
                            <View style={styles.avatarContainer}>
                                <LinearGradient
                                    colors={["#ffffff", "#f0f0f0"]}
                                    style={styles.avatar}
                                >
                                    <Text style={styles.avatarText}>U</Text>
                                </LinearGradient>
                            </View>
                            <Text style={styles.userName}>Kullanıcı Adı</Text>
                            <Text style={styles.userEmail}><EMAIL></Text>
                        </View>

                        <View style={styles.statsContainer}>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>127</Text>
                                <Text style={styles.statLabel}>Kelime</Text>
                            </View>
                            <View style={styles.statDivider} />
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>15</Text>
                                <Text style={styles.statLabel}>Quiz</Text>
                            </View>
                            <View style={styles.statDivider} />
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>7</Text>
                                <Text style={styles.statLabel}>Gün Streak</Text>
                            </View>
                        </View>
                    </LinearGradient>

                    {/* Settings */}
                    <View style={styles.section}>
                        <Text style={styles.sectionTitle}>⚙️ Ayarlar</Text>

                        <View style={styles.settingsContainer}>
                            <TouchableOpacity style={styles.settingItem}>
                                <View style={styles.settingContent}>
                                    <View style={styles.settingIcon}>
                                        <Text style={styles.settingEmoji}>🔔</Text>
                                    </View>
                                    <Text style={styles.settingText}>Bildirimler</Text>
                                </View>
                                <Text style={styles.settingArrow}>›</Text>
                            </TouchableOpacity>

                            <TouchableOpacity style={styles.settingItem}>
                                <View style={styles.settingContent}>
                                    <View style={styles.settingIcon}>
                                        <Text style={styles.settingEmoji}>🌍</Text>
                                    </View>
                                    <Text style={styles.settingText}>Dil Ayarları</Text>
                                </View>
                                <Text style={styles.settingArrow}>›</Text>
                            </TouchableOpacity>

                            <TouchableOpacity style={styles.settingItem}>
                                <View style={styles.settingContent}>
                                    <View style={styles.settingIcon}>
                                        <Text style={styles.settingEmoji}>📊</Text>
                                    </View>
                                    <Text style={styles.settingText}>İstatistikler</Text>
                                </View>
                                <Text style={styles.settingArrow}>›</Text>
                            </TouchableOpacity>

                            <TouchableOpacity style={styles.settingItem}>
                                <View style={styles.settingContent}>
                                    <View style={styles.settingIcon}>
                                        <Text style={styles.settingEmoji}>💾</Text>
                                    </View>
                                    <Text style={styles.settingText}>Veri Yedekleme</Text>
                                </View>
                                <Text style={styles.settingArrow}>›</Text>
                            </TouchableOpacity>

                            <TouchableOpacity style={styles.settingItem}>
                                <View style={styles.settingContent}>
                                    <View style={styles.settingIcon}>
                                        <Text style={styles.settingEmoji}>❓</Text>
                                    </View>
                                    <Text style={styles.settingText}>Yardım & Destek</Text>
                                </View>
                                <Text style={styles.settingArrow}>›</Text>
                            </TouchableOpacity>
                        </View>
                    </View>

                    {/* Learning Goals */}
                    <LinearGradient
                        colors={["#10b981", "#059669"]}
                        style={styles.goalsCard}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                    >
                        <Text style={styles.goalsTitle}>🎯 Öğrenme Hedefleri</Text>
                        <View style={styles.goalsContainer}>
                            <View style={styles.goalItem}>
                                <Text style={styles.goalLabel}>Günlük kelime hedefi</Text>
                                <Text style={styles.goalValue}>10 kelime</Text>
                            </View>
                            <View style={styles.goalDivider} />
                            <View style={styles.goalItem}>
                                <Text style={styles.goalLabel}>Haftalık quiz hedefi</Text>
                                <Text style={styles.goalValue}>5 quiz</Text>
                            </View>
                            <View style={styles.goalDivider} />
                            <View style={styles.goalItem}>
                                <Text style={styles.goalLabel}>Okuma süresi</Text>
                                <Text style={styles.goalValue}>30 dakika</Text>
                            </View>
                        </View>
                    </LinearGradient>

                    {/* Logout Button */}
                    <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
                        <LinearGradient
                            colors={["#ef4444", "#dc2626"]}
                            style={styles.logoutGradient}
                            start={{ x: 0, y: 0 }}
                            end={{ x: 1, y: 1 }}
                        >
                            <Text style={styles.logoutIcon}>🚪</Text>
                            <Text style={styles.logoutText}>Çıkış Yap</Text>
                        </LinearGradient>
                    </TouchableOpacity>
                </View>
            </ScrollView>
        </LinearGradient>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    scrollView: {
        flex: 1,
    },
    content: {
        paddingHorizontal: 24,
        paddingTop: 64,
        paddingBottom: 120,
    },
    header: {
        marginBottom: 32,
    },
    title: {
        fontSize: 28,
        fontWeight: "bold",
        color: "#1F2937",
        marginBottom: 8,
    },
    subtitle: {
        fontSize: 18,
        color: "#6B7280",
    },
    userCard: {
        borderRadius: 20,
        padding: 24,
        marginBottom: 32,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 10 },
        shadowOpacity: 0.3,
        shadowRadius: 20,
        elevation: 10,
    },
    userInfo: {
        alignItems: "center",
        marginBottom: 24,
    },
    avatarContainer: {
        marginBottom: 16,
    },
    avatar: {
        width: 80,
        height: 80,
        borderRadius: 40,
        alignItems: "center",
        justifyContent: "center",
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 5,
    },
    avatarText: {
        fontSize: 32,
        fontWeight: "bold",
        color: "#667eea",
    },
    userName: {
        fontSize: 24,
        fontWeight: "bold",
        color: "white",
        marginBottom: 4,
    },
    userEmail: {
        fontSize: 16,
        color: "rgba(255, 255, 255, 0.8)",
    },
    statsContainer: {
        flexDirection: "row",
        backgroundColor: "rgba(255, 255, 255, 0.2)",
        borderRadius: 16,
        padding: 20,
        alignItems: "center",
        justifyContent: "space-between",
    },
    statItem: {
        alignItems: "center",
        flex: 1,
    },
    statNumber: {
        fontSize: 24,
        fontWeight: "bold",
        color: "white",
        marginBottom: 4,
    },
    statLabel: {
        fontSize: 14,
        color: "rgba(255, 255, 255, 0.8)",
        fontWeight: "500",
    },
    statDivider: {
        width: 1,
        height: 40,
        backgroundColor: "rgba(255, 255, 255, 0.3)",
        marginHorizontal: 16,
    },
    section: {
        marginBottom: 32,
    },
    sectionTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: "#1F2937",
        marginBottom: 16,
    },
    settingsContainer: {
        backgroundColor: "white",
        borderRadius: 16,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 12,
        elevation: 5,
    },
    settingItem: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        paddingHorizontal: 20,
        paddingVertical: 16,
        borderBottomWidth: 1,
        borderBottomColor: "#F3F4F6",
    },
    settingContent: {
        flexDirection: "row",
        alignItems: "center",
        flex: 1,
    },
    settingIcon: {
        backgroundColor: "#F3F4F6",
        borderRadius: 10,
        padding: 8,
        marginRight: 12,
    },
    settingEmoji: {
        fontSize: 18,
    },
    settingText: {
        fontSize: 16,
        color: "#1F2937",
        fontWeight: "500",
    },
    settingArrow: {
        fontSize: 18,
        color: "#9CA3AF",
        fontWeight: "300",
    },
    goalsCard: {
        borderRadius: 20,
        padding: 24,
        marginBottom: 32,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.2,
        shadowRadius: 16,
        elevation: 8,
    },
    goalsTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: "white",
        textAlign: "center",
        marginBottom: 20,
    },
    goalsContainer: {
        gap: 16,
    },
    goalItem: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        backgroundColor: "rgba(255, 255, 255, 0.2)",
        borderRadius: 12,
        padding: 16,
    },
    goalLabel: {
        fontSize: 16,
        color: "rgba(255, 255, 255, 0.9)",
        fontWeight: "500",
    },
    goalValue: {
        fontSize: 16,
        color: "white",
        fontWeight: "bold",
    },
    goalDivider: {
        height: 1,
        backgroundColor: "rgba(255, 255, 255, 0.2)",
    },
    logoutButton: {
        borderRadius: 16,
        shadowColor: "#ef4444",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 5,
    },
    logoutGradient: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        padding: 20,
        borderRadius: 16,
    },
    logoutIcon: {
        fontSize: 20,
        marginRight: 12,
    },
    logoutText: {
        color: "white",
        fontSize: 18,
        fontWeight: "bold",
    },
});

export default ProfileScreen;
