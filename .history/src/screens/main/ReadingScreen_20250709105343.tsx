import React from "react";
import { View, Text, TouchableOpacity, ScrollView, StyleSheet } from "react-native";
import { LinearGradient } from "expo-linear-gradient";

const ReadingScreen: React.FC = () => {
    return (
        <LinearGradient colors={["#f8fafc", "#e2e8f0"]} style={styles.container}>
            <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                <View style={styles.content}>
                    {/* Header */}
                    <View style={styles.header}>
                        <Text style={styles.title}>Okuma Merkezi 📖</Text>
                        <Text style={styles.subtitle}>
                            PDF dosyalarınızı yükleyin ve okumaya başlayın
                        </Text>
                    </View>

                    {/* Upload Section */}
                    <LinearGradient
                        colors={["#4facfe", "#00f2fe"]}
                        style={styles.uploadCard}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                    >
                        <View style={styles.uploadContent}>
                            <View style={styles.uploadIcon}>
                                <Text style={styles.uploadEmoji}>📄</Text>
                            </View>
                            <Text style={styles.uploadTitle}>PDF Dosyası Yükle</Text>
                            <Text style={styles.uploadSubtitle}>
                                Okumak istediğiniz PDF dosyasını seçin ve kelime öğrenmeye başlayın
                            </Text>
                            <TouchableOpacity style={styles.uploadButton}>
                                <Text style={styles.uploadButtonText}>📁 Dosya Seç</Text>
                            </TouchableOpacity>
                        </View>
                    </LinearGradient>

                {/* Recent Documents */}
                <View>
                    <Text className="text-lg font-semibold text-gray-900 mb-4">
                        Son Okuduklarım
                    </Text>

                    <View className="space-y-3">
                        <View className="bg-gray-50 rounded-xl p-4 border border-gray-200">
                            <Text className="font-semibold text-gray-900 mb-1">
                                Örnek Doküman 1
                            </Text>
                            <Text className="text-gray-600 text-sm mb-2">
                                Son okuma: 2 saat önce
                            </Text>
                            <View className="flex-row justify-between items-center">
                                <Text className="text-blue-600 text-sm">%75 tamamlandı</Text>
                                <TouchableOpacity className="bg-blue-500 px-4 py-2 rounded-lg">
                                    <Text className="text-white text-sm font-semibold">
                                        Devam Et
                                    </Text>
                                </TouchableOpacity>
                            </View>
                        </View>

                        <View className="bg-gray-50 rounded-xl p-4 border border-gray-200">
                            <Text className="font-semibold text-gray-900 mb-1">
                                Örnek Doküman 2
                            </Text>
                            <Text className="text-gray-600 text-sm mb-2">
                                Son okuma: 1 gün önce
                            </Text>
                            <View className="flex-row justify-between items-center">
                                <Text className="text-blue-600 text-sm">%45 tamamlandı</Text>
                                <TouchableOpacity className="bg-blue-500 px-4 py-2 rounded-lg">
                                    <Text className="text-white text-sm font-semibold">
                                        Devam Et
                                    </Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </View>

                {/* Reading Tips */}
                <View className="mt-8 bg-green-50 rounded-xl p-6">
                    <Text className="text-lg font-semibold text-green-900 mb-2">
                        💡 Okuma İpuçları
                    </Text>
                    <Text className="text-green-700">
                        • Bilmediğiniz kelimelere dokunarak anlamlarını öğrenin{"\n"}• Önemli
                        kelimeleri kelime listenize ekleyin{"\n"}• Düzenli okuma alışkanlığı edinin
                    </Text>
                </View>
            </View>
        </ScrollView>
    );
};

export default ReadingScreen;
