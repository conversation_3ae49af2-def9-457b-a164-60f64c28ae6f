import React from "react";
import { View, Text, ScrollView, StyleSheet, TouchableOpacity } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useNavigation } from "@react-navigation/native";
import { authService } from "../../services/auth";

const HomeScreen: React.FC = () => {
    const navigation = useNavigation();

    const handleLogout = async () => {
        try {
            await authService.signOut();
        } catch (error) {
            console.error("Logout error:", error);
        }
    };

    return (
        <LinearGradient colors={["#f8fafc", "#e2e8f0"]} style={styles.container}>
            <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                <View style={styles.content}>
                    {/* Header */}
                    <View style={styles.header}>
                        <View style={styles.headerText}>
                            <Text style={styles.title}>Hoş Geldiniz! 👋</Text>
                            <Text style={styles.subtitle}>
                                Di<PERSON> <PERSON>ğrenme yolculuğunuza devam edin
                            </Text>
                        </View>
                        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
                            <Text style={styles.logoutButtonText}>🚪 Çıkış</Text>
                        </TouchableOpacity>
                    </View>

                    {/* Quick Stats */}
                    <LinearGradient
                        colors={["#667eea", "#764ba2"]}
                        style={styles.statsCard}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                    >
                        <Text style={styles.statsTitle}>📊 Günlük İlerleme</Text>
                        <View style={styles.statsContainer}>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>12</Text>
                                <Text style={styles.statLabel}>Yeni Kelime</Text>
                            </View>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>3</Text>
                                <Text style={styles.statLabel}>Tamamlanan Quiz</Text>
                            </View>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>25</Text>
                                <Text style={styles.statLabel}>Dakika Okuma</Text>
                            </View>
                        </View>
                    </LinearGradient>

                    {/* Quick Actions */}
                    <View style={styles.actionsSection}>
                        <Text style={styles.sectionTitle}>🚀 Hızlı Erişim</Text>

                        <View style={styles.actionsContainer}>
                            <TouchableOpacity
                                style={styles.actionCard}
                                onPress={() => navigation.navigate("Reading" as never)}
                            >
                                <LinearGradient
                                    colors={["#4facfe", "#00f2fe"]}
                                    style={styles.actionGradient}
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 1, y: 1 }}
                                >
                                    <View style={styles.actionContent}>
                                        <View style={styles.actionTextContainer}>
                                            <Text style={styles.actionTitle}>📖 Okumaya Başla</Text>
                                            <Text style={styles.actionSubtitle}>
                                                PDF dosyalarınızı yükleyin ve okuyun
                                            </Text>
                                        </View>
                                        <View style={styles.actionIcon}>
                                            <Text style={styles.actionEmoji}>📖</Text>
                                        </View>
                                    </View>
                                </LinearGradient>
                            </TouchableOpacity>

                            <TouchableOpacity
                                style={styles.actionCard}
                                onPress={() => navigation.navigate("Quiz" as never)}
                            >
                                <LinearGradient
                                    colors={["#43e97b", "#38f9d7"]}
                                    style={styles.actionGradient}
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 1, y: 1 }}
                                >
                                    <View style={styles.actionContent}>
                                        <View style={styles.actionTextContainer}>
                                            <Text style={styles.actionTitle}>🧠 Quiz Çöz</Text>
                                            <Text style={styles.actionSubtitle}>
                                                Öğrendiğiniz kelimeleri test edin
                                            </Text>
                                        </View>
                                        <View style={styles.actionIcon}>
                                            <Text style={styles.actionEmoji}>🧠</Text>
                                        </View>
                                    </View>
                                </LinearGradient>
                            </TouchableOpacity>

                            <TouchableOpacity
                                style={styles.actionCard}
                                onPress={() => navigation.navigate("WordList" as never)}
                            >
                                <LinearGradient
                                    colors={["#fa709a", "#fee140"]}
                                    style={styles.actionGradient}
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 1, y: 1 }}
                                >
                                    <View style={styles.actionContent}>
                                        <View style={styles.actionTextContainer}>
                                            <Text style={styles.actionTitle}>📝 Kelime Listem</Text>
                                            <Text style={styles.actionSubtitle}>
                                                Kaydettiğiniz kelimeleri görüntüleyin
                                            </Text>
                                        </View>
                                        <View style={styles.actionIcon}>
                                            <Text style={styles.actionEmoji}>📝</Text>
                                        </View>
                                    </View>
                                </LinearGradient>
                            </TouchableOpacity>
                        </View>
                    </View>

                    {/* Recent Activity */}
                    <View style={styles.activityCard}>
                        <Text style={styles.activityTitle}>📈 Son Aktiviteler</Text>
                        <View style={styles.activityContent}>
                            <Text style={styles.activityIcon}>📚</Text>
                            <Text style={styles.activityText}>Henüz aktivite bulunmuyor</Text>
                            <Text style={styles.activitySubtext}>
                                Okuma veya quiz yapmaya başlayın!
                            </Text>
                        </View>
                    </View>
                </View>
            </ScrollView>
        </LinearGradient>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    scrollView: {
        flex: 1,
    },
    content: {
        paddingHorizontal: 24,
        paddingTop: 64,
        paddingBottom: 120, // Tab bar yüksekliği + extra space
    },
    header: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: 32,
    },
    headerText: {
        flex: 1,
    },
    title: {
        fontSize: 28,
        fontWeight: "bold",
        color: "#1F2937",
        marginBottom: 4,
    },
    subtitle: {
        fontSize: 18,
        color: "#6B7280",
    },
    logoutButton: {
        backgroundColor: "#EF4444",
        borderRadius: 12,
        paddingHorizontal: 16,
        paddingVertical: 8,
        shadowColor: "#EF4444",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 4,
        elevation: 3,
    },
    logoutButtonText: {
        color: "white",
        fontSize: 14,
        fontWeight: "600",
    },
    statsCard: {
        borderRadius: 20,
        padding: 24,
        marginBottom: 24,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 10 },
        shadowOpacity: 0.3,
        shadowRadius: 20,
        elevation: 10,
    },
    statsTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: "white",
        textAlign: "center",
        marginBottom: 24,
    },
    statsContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
    },
    statItem: {
        alignItems: "center",
    },
    statNumber: {
        fontSize: 28,
        fontWeight: "bold",
        color: "white",
        marginBottom: 4,
    },
    statLabel: {
        fontSize: 14,
        color: "rgba(255, 255, 255, 0.8)",
        fontWeight: "500",
        textAlign: "center",
    },
    actionsSection: {
        marginBottom: 32,
    },
    sectionTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: "#1F2937",
        marginBottom: 24,
    },
    actionsContainer: {
        gap: 16,
    },
    actionCard: {
        borderRadius: 20,
        overflow: "hidden",
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.2,
        shadowRadius: 16,
        elevation: 8,
    },
    actionGradient: {
        padding: 24,
    },
    actionContent: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
    },
    actionTextContainer: {
        flex: 1,
    },
    actionTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: "white",
        marginBottom: 8,
    },
    actionSubtitle: {
        fontSize: 16,
        color: "rgba(255, 255, 255, 0.8)",
    },
    actionIcon: {
        backgroundColor: "rgba(255, 255, 255, 0.2)",
        borderRadius: 50,
        padding: 12,
        marginLeft: 16,
    },
    actionEmoji: {
        fontSize: 28,
    },
    activityCard: {
        backgroundColor: "white",
        borderRadius: 20,
        padding: 24,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.1,
        shadowRadius: 16,
        elevation: 5,
    },
    activityTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: "#1F2937",
        marginBottom: 16,
    },
    activityContent: {
        backgroundColor: "#F9FAFB",
        borderRadius: 16,
        padding: 24,
        alignItems: "center",
    },
    activityIcon: {
        fontSize: 60,
        marginBottom: 16,
    },
    activityText: {
        fontSize: 18,
        color: "#6B7280",
        textAlign: "center",
        marginBottom: 8,
    },
    activitySubtext: {
        fontSize: 14,
        color: "#9CA3AF",
        textAlign: "center",
    },
});

export default HomeScreen;
