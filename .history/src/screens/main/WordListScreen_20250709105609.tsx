import React, { useState } from "react";
import { View, Text, TouchableOpacity, ScrollView, TextInput, StyleSheet } from "react-native";
import { LinearGradient } from "expo-linear-gradient";

const WordListScreen: React.FC = () => {
    const [searchText, setSearchText] = useState("");

    const sampleWords = [
        {
            word: "serendipity",
            meaning: "tesadüf, şans eseri karşılaşma",
            example: "It was pure serendipity that we met.",
            level: "learned",
            category: "noun",
        },
        {
            word: "ephemeral",
            meaning: "geçici, kısa süreli",
            example: "The beauty of cherry blossoms is ephemeral.",
            level: "learning",
            category: "adjective",
        },
        {
            word: "ubiquitous",
            meaning: "her yerde bulunan, yaygın",
            example: "Smartphones are ubiquitous nowadays.",
            level: "learned",
            category: "adjective",
        },
        {
            word: "mellifluous",
            meaning: "tatlı sesli, akı<PERSON><PERSON>",
            example: "Her mellifluous voice captivated the audience.",
            level: "learning",
            category: "adjective",
        },
    ];

    return (
        <LinearGradient colors={["#f8fafc", "#e2e8f0"]} style={styles.container}>
            <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                <View style={styles.content}>
                    {/* Header */}
                    <View style={styles.header}>
                        <Text style={styles.title}>Kelime Koleksiyonum 📝</Text>
                        <Text style={styles.subtitle}>
                            Öğrendiğiniz kelimeleri takip edin ve gelişiminizi izleyin
                        </Text>
                    </View>

                    {/* Search Bar */}
                    <View style={styles.searchContainer}>
                        <TextInput
                            style={styles.searchInput}
                            placeholder="Kelime ara..."
                            placeholderTextColor="#9CA3AF"
                            value={searchText}
                            onChangeText={setSearchText}
                        />
                        <View style={styles.searchIcon}>
                            <Text style={styles.searchEmoji}>🔍</Text>
                        </View>
                    </View>

                    {/* Stats */}
                    <LinearGradient
                        colors={["#667eea", "#764ba2"]}
                        style={styles.statsCard}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                    >
                        <Text style={styles.statsTitle}>📊 Kelime İstatistikleri</Text>
                        <View style={styles.statsContainer}>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>127</Text>
                                <Text style={styles.statLabel}>Toplam Kelime</Text>
                            </View>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>89</Text>
                                <Text style={styles.statLabel}>Öğrenilen</Text>
                            </View>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>38</Text>
                                <Text style={styles.statLabel}>Öğreniliyor</Text>
                            </View>
                        </View>
                    </LinearGradient>

                    {/* Word List */}
                    <View style={styles.section}>
                        <Text style={styles.sectionTitle}>📚 Kelimelerim</Text>

                        <View style={styles.wordsContainer}>
                            {sampleWords.map((item, index) => (
                                <View key={index} style={styles.wordCard}>
                                    <View style={styles.wordHeader}>
                                        <View style={styles.wordTitleContainer}>
                                            <Text style={styles.wordTitle}>{item.word}</Text>
                                            <View style={styles.categoryBadge}>
                                                <Text style={styles.categoryText}>
                                                    {item.category}
                                                </Text>
                                            </View>
                                        </View>
                                        <View
                                            style={[
                                                styles.levelBadge,
                                                item.level === "learned"
                                                    ? styles.learnedBadge
                                                    : styles.learningBadge,
                                            ]}
                                        >
                                            <Text
                                                style={[
                                                    styles.levelText,
                                                    item.level === "learned"
                                                        ? styles.learnedText
                                                        : styles.learningText,
                                                ]}
                                            >
                                                {item.level === "learned"
                                                    ? "✓ Öğrenildi"
                                                    : "📖 Öğreniliyor"}
                                            </Text>
                                        </View>
                                    </View>

                                    <Text style={styles.wordMeaning}>{item.meaning}</Text>

                                    <View style={styles.exampleContainer}>
                                        <Text style={styles.exampleLabel}>Örnek:</Text>
                                        <Text style={styles.exampleText}>"{item.example}"</Text>
                                    </View>

                                    <View style={styles.actionButtons}>
                                        <TouchableOpacity style={styles.quizButton}>
                                            <Text style={styles.quizButtonText}>
                                                🧠 Quiz'e Ekle
                                            </Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={styles.deleteButton}>
                                            <Text style={styles.deleteButtonText}>🗑️</Text>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            ))}
                        </View>
                    </View>

                    {/* Add Word Button */}
                    <LinearGradient
                        colors={["#fa709a", "#fee140"]}
                        style={styles.addButton}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                    >
                        <TouchableOpacity style={styles.addButtonTouchable}>
                            <Text style={styles.addButtonIcon}>➕</Text>
                            <Text style={styles.addButtonText}>Yeni Kelime Ekle</Text>
                        </TouchableOpacity>
                    </LinearGradient>
                </View>
            </ScrollView>
        </LinearGradient>
    );
};

export default WordListScreen;
