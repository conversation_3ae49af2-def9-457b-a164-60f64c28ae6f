import React from "react";
import { View, Text, TouchableOpacity, ScrollView, StyleSheet } from "react-native";
import { LinearGradient } from "expo-linear-gradient";

const ReadingScreen: React.FC = () => {
    return (
        <LinearGradient colors={["#f8fafc", "#e2e8f0"]} style={styles.container}>
            <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                <View style={styles.content}>
                    {/* Header */}
                    <View style={styles.header}>
                        <Text style={styles.title}>Okuma Merkezi 📖</Text>
                        <Text style={styles.subtitle}>
                            PDF dosyalarınızı yükleyin ve okumaya başlayın
                        </Text>
                    </View>

                    {/* Upload Section */}
                    <LinearGradient
                        colors={["#4facfe", "#00f2fe"]}
                        style={styles.uploadCard}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                    >
                        <View style={styles.uploadContent}>
                            <View style={styles.uploadIcon}>
                                <Text style={styles.uploadEmoji}>📄</Text>
                            </View>
                            <Text style={styles.uploadTitle}>PDF Dosyası Yükle</Text>
                            <Text style={styles.uploadSubtitle}>
                                Okumak istediğiniz PDF dosyasını seçin ve kelime öğrenmeye başlayın
                            </Text>
                            <TouchableOpacity style={styles.uploadButton}>
                                <Text style={styles.uploadButtonText}>📁 Dosya Seç</Text>
                            </TouchableOpacity>
                        </View>
                    </LinearGradient>

                    {/* Recent Documents */}
                    <View style={styles.section}>
                        <Text style={styles.sectionTitle}>📚 Son Okuduklarım</Text>

                        <View style={styles.documentsContainer}>
                            <View style={styles.documentCard}>
                                <View style={styles.documentHeader}>
                                    <View style={styles.documentIcon}>
                                        <Text style={styles.documentEmoji}>📄</Text>
                                    </View>
                                    <View style={styles.documentInfo}>
                                        <Text style={styles.documentTitle}>
                                            İngilizce Hikayeler
                                        </Text>
                                        <Text style={styles.documentSubtitle}>
                                            Son okuma: 2 saat önce
                                        </Text>
                                    </View>
                                </View>
                                <View style={styles.progressContainer}>
                                    <View style={styles.progressBar}>
                                        <View style={[styles.progressFill, { width: "75%" }]} />
                                    </View>
                                    <Text style={styles.progressText}>%75 tamamlandı</Text>
                                </View>
                                <TouchableOpacity style={styles.continueButton}>
                                    <Text style={styles.continueButtonText}>Devam Et</Text>
                                </TouchableOpacity>
                            </View>

                            <View style={styles.documentCard}>
                                <View style={styles.documentHeader}>
                                    <View style={styles.documentIcon}>
                                        <Text style={styles.documentEmoji}>📖</Text>
                                    </View>
                                    <View style={styles.documentInfo}>
                                        <Text style={styles.documentTitle}>Teknik Makale</Text>
                                        <Text style={styles.documentSubtitle}>
                                            Son okuma: 1 gün önce
                                        </Text>
                                    </View>
                                </View>
                                <View style={styles.progressContainer}>
                                    <View style={styles.progressBar}>
                                        <View style={[styles.progressFill, { width: "45%" }]} />
                                    </View>
                                    <Text style={styles.progressText}>%45 tamamlandı</Text>
                                </View>
                                <TouchableOpacity style={styles.continueButton}>
                                    <Text style={styles.continueButtonText}>Devam Et</Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>

                    {/* Reading Tips */}
                    <LinearGradient
                        colors={["#a8edea", "#fed6e3"]}
                        style={styles.tipsCard}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                    >
                        <Text style={styles.tipsTitle}>💡 Okuma İpuçları</Text>
                        <View style={styles.tipsContainer}>
                            <View style={styles.tipItem}>
                                <Text style={styles.tipIcon}>👆</Text>
                                <Text style={styles.tipText}>
                                    Bilmediğiniz kelimelere dokunarak anlamlarını öğrenin
                                </Text>
                            </View>
                            <View style={styles.tipItem}>
                                <Text style={styles.tipIcon}>📝</Text>
                                <Text style={styles.tipText}>
                                    Önemli kelimeleri kelime listenize ekleyin
                                </Text>
                            </View>
                            <View style={styles.tipItem}>
                                <Text style={styles.tipIcon}>📅</Text>
                                <Text style={styles.tipText}>Düzenli okuma alışkanlığı edinin</Text>
                            </View>
                        </View>
                    </LinearGradient>
                </View>
            </ScrollView>
        </LinearGradient>
    );
};

export default ReadingScreen;
