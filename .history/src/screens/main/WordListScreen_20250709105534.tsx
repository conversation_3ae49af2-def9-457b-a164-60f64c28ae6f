import React, { useState } from "react";
import { View, Text, TouchableOpacity, ScrollView, TextInput, StyleSheet } from "react-native";
import { LinearGradient } from "expo-linear-gradient";

const WordListScreen: React.FC = () => {
    const [searchText, setSearchText] = useState("");

    const sampleWords = [
        {
            word: "serendipity",
            meaning: "tesadüf, şans eseri karşılaşma",
            example: "It was pure serendipity that we met.",
            level: "learned",
            category: "noun"
        },
        {
            word: "ephemeral",
            meaning: "geçici, kısa süreli",
            example: "The beauty of cherry blossoms is ephemeral.",
            level: "learning",
            category: "adjective"
        },
        {
            word: "ubiquitous",
            meaning: "her yerde bulunan, yaygın",
            example: "Smartphones are ubiquitous nowadays.",
            level: "learned",
            category: "adjective"
        },
        {
            word: "mellifluous",
            meaning: "tatlı sesli, akı<PERSON><PERSON>",
            example: "Her mellifluous voice captivated the audience.",
            level: "learning",
            category: "adjective"
        },
    ];

    return (
        <LinearGradient colors={["#f8fafc", "#e2e8f0"]} style={styles.container}>
            <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                <View style={styles.content}>
                    {/* Header */}
                    <View style={styles.header}>
                        <Text style={styles.title}>Kelime Koleksiyonum 📝</Text>
                        <Text style={styles.subtitle}>
                            Öğrendiğiniz kelimeleri takip edin ve gelişiminizi izleyin
                        </Text>
                    </View>

                    {/* Search Bar */}
                    <View style={styles.searchContainer}>
                        <TextInput
                            style={styles.searchInput}
                            placeholder="Kelime ara..."
                            placeholderTextColor="#9CA3AF"
                            value={searchText}
                            onChangeText={setSearchText}
                        />
                        <View style={styles.searchIcon}>
                            <Text style={styles.searchEmoji}>🔍</Text>
                        </View>
                    </View>

                    {/* Stats */}
                    <LinearGradient
                        colors={["#667eea", "#764ba2"]}
                        style={styles.statsCard}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                    >
                        <Text style={styles.statsTitle}>📊 Kelime İstatistikleri</Text>
                        <View style={styles.statsContainer}>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>127</Text>
                                <Text style={styles.statLabel}>Toplam Kelime</Text>
                            </View>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>89</Text>
                                <Text style={styles.statLabel}>Öğrenilen</Text>
                            </View>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>38</Text>
                                <Text style={styles.statLabel}>Öğreniliyor</Text>
                            </View>
                        </View>
                    </LinearGradient>

                {/* Word List */}
                <View>
                    <Text className="text-lg font-semibold text-gray-900 mb-4">Kelimelerim</Text>

                    <View className="space-y-3">
                        {sampleWords.map((item, index) => (
                            <View
                                key={index}
                                className="bg-white rounded-xl p-4 border border-gray-200 shadow-sm"
                            >
                                <View className="flex-row justify-between items-start mb-2">
                                    <Text className="text-lg font-semibold text-gray-900">
                                        {item.word}
                                    </Text>
                                    <View className="bg-green-100 px-2 py-1 rounded">
                                        <Text className="text-green-700 text-xs font-semibold">
                                            Öğrenildi
                                        </Text>
                                    </View>
                                </View>

                                <Text className="text-blue-600 font-medium mb-2">
                                    {item.meaning}
                                </Text>

                                <Text className="text-gray-600 text-sm italic">
                                    "{item.example}"
                                </Text>

                                <View className="flex-row justify-end mt-3 space-x-2">
                                    <TouchableOpacity className="bg-blue-500 px-3 py-1 rounded">
                                        <Text className="text-white text-sm">Quiz'e Ekle</Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity className="bg-red-500 px-3 py-1 rounded">
                                        <Text className="text-white text-sm">Sil</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        ))}
                    </View>
                </View>

                {/* Add Word Button */}
                <TouchableOpacity className="bg-purple-500 rounded-xl p-4 mt-6">
                    <Text className="text-white font-semibold text-center text-lg">
                        + Yeni Kelime Ekle
                    </Text>
                </TouchableOpacity>
            </View>
        </ScrollView>
    );
};

export default WordListScreen;
