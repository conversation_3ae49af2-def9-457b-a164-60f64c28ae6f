import React from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { authService } from '../../services/auth';

const HomeScreen: React.FC = () => {
  const navigation = useNavigation();

  const handleLogout = async () => {
    try {
      await authService.signOut();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <ScrollView className="flex-1 bg-white">
      <View className="px-6 pt-12 pb-6">
        {/* Header */}
        <View className="flex-row justify-between items-center mb-8">
          <View>
            <Text className="text-2xl font-bold text-gray-900">
              Hoş Geldiniz! 👋
            </Text>
            <Text className="text-gray-600 mt-1">
              Dil öğrenme yolculuğunuza devam edin
            </Text>
          </View>
          <TouchableOpacity
            onPress={handleLogout}
            className="bg-red-500 px-4 py-2 rounded-lg"
          >
            <Text className="text-white font-semibold"><PERSON><PERSON>k<PERSON>ş</Text>
          </TouchableOpacity>
        </View>

        {/* Quick Stats */}
        <View className="bg-primary-50 rounded-xl p-6 mb-6">
          <Text className="text-lg font-semibold text-primary-900 mb-4">
            Günlük İlerleme
          </Text>
          <View className="flex-row justify-between">
            <View className="items-center">
              <Text className="text-2xl font-bold text-primary-600">12</Text>
              <Text className="text-primary-700 text-sm">Yeni Kelime</Text>
            </View>
            <View className="items-center">
              <Text className="text-2xl font-bold text-primary-600">3</Text>
              <Text className="text-primary-700 text-sm">Tamamlanan Quiz</Text>
            </View>
            <View className="items-center">
              <Text className="text-2xl font-bold text-primary-600">25</Text>
              <Text className="text-primary-700 text-sm">Dakika Okuma</Text>
            </View>
          </View>
        </View>

        {/* Quick Actions */}
        <View className="space-y-4">
          <Text className="text-lg font-semibold text-gray-900 mb-2">
            Hızlı Erişim
          </Text>
          
          <TouchableOpacity
            className="bg-blue-500 rounded-xl p-4 flex-row items-center justify-between"
            onPress={() => navigation.navigate('Reading' as never)}
          >
            <View>
              <Text className="text-white font-semibold text-lg">Okumaya Başla</Text>
              <Text className="text-blue-100">PDF dosyalarınızı yükleyin ve okuyun</Text>
            </View>
            <Text className="text-white text-2xl">📖</Text>
          </TouchableOpacity>

          <TouchableOpacity
            className="bg-green-500 rounded-xl p-4 flex-row items-center justify-between"
            onPress={() => navigation.navigate('Quiz' as never)}
          >
            <View>
              <Text className="text-white font-semibold text-lg">Quiz Çöz</Text>
              <Text className="text-green-100">Öğrendiğiniz kelimeleri test edin</Text>
            </View>
            <Text className="text-white text-2xl">🧠</Text>
          </TouchableOpacity>

          <TouchableOpacity
            className="bg-purple-500 rounded-xl p-4 flex-row items-center justify-between"
            onPress={() => navigation.navigate('WordList' as never)}
          >
            <View>
              <Text className="text-white font-semibold text-lg">Kelime Listem</Text>
              <Text className="text-purple-100">Kaydettiğiniz kelimeleri görüntüleyin</Text>
            </View>
            <Text className="text-white text-2xl">📝</Text>
          </TouchableOpacity>
        </View>

        {/* Recent Activity */}
        <View className="mt-8">
          <Text className="text-lg font-semibold text-gray-900 mb-4">
            Son Aktiviteler
          </Text>
          <View className="bg-gray-50 rounded-xl p-4">
            <Text className="text-gray-600 text-center">
              Henüz aktivite bulunmuyor
            </Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

export default HomeScreen;
