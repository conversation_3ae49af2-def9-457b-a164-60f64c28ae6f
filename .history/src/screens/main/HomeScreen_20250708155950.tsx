import React from "react";
import { View, Text, ScrollView } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useNavigation } from "@react-navigation/native";
import { authService } from "../../services/auth";
import Card from "../../components/ui/Card";
import Button from "../../components/ui/Button";

const HomeScreen: React.FC = () => {
    const navigation = useNavigation();

    const handleLogout = async () => {
        try {
            await authService.signOut();
        } catch (error) {
            console.error("Logout error:", error);
        }
    };

    return (
        <LinearGradient
            colors={['#f8fafc', '#e2e8f0']}
            className="flex-1"
        >
            <ScrollView
                className="flex-1"
                showsVerticalScrollIndicator={false}
            >
                <View className="px-6 pt-16 pb-6">
                    {/* Header */}
                    <View className="flex-row justify-between items-center mb-8">
                        <View>
                            <Text className="text-3xl font-bold text-gray-900">
                                Hoş Geldiniz! 👋
                            </Text>
                            <Text className="text-gray-600 mt-1 text-lg">
                                Dil öğrenme yolculuğunuza devam edin
                            </Text>
                        </View>
                        <Button
                            title="Çıkış"
                            onPress={handleLogout}
                            variant="danger"
                            size="small"
                            icon="🚪"
                        />
                    </View>

                    {/* Quick Stats */}
                    <Card variant="gradient" gradientColors={['#667eea', '#764ba2']} className="mb-6">
                        <Text className="text-xl font-bold text-white mb-6 text-center">
                            📊 Günlük İlerleme
                        </Text>
                        <View className="flex-row justify-between">
                            <View className="items-center">
                                <Text className="text-3xl font-bold text-white">12</Text>
                                <Text className="text-white/80 text-sm font-medium">Yeni Kelime</Text>
                            </View>
                            <View className="items-center">
                                <Text className="text-3xl font-bold text-white">3</Text>
                                <Text className="text-white/80 text-sm font-medium">Tamamlanan Quiz</Text>
                            </View>
                            <View className="items-center">
                                <Text className="text-3xl font-bold text-white">25</Text>
                                <Text className="text-white/80 text-sm font-medium">Dakika Okuma</Text>
                            </View>
                        </View>
                    </Card>

                {/* Quick Actions */}
                <View className="mb-8">
                    <Text className="text-xl font-bold text-gray-900 mb-6">
                        🚀 Hızlı Erişim
                    </Text>

                    <View className="space-y-4">
                        <Card
                            variant="gradient"
                            gradientColors={['#4facfe', '#00f2fe']}
                            onPress={() => navigation.navigate("Reading" as never)}
                        >
                            <View className="flex-row items-center justify-between">
                                <View className="flex-1">
                                    <Text className="text-white font-bold text-xl mb-2">
                                        📖 Okumaya Başla
                                    </Text>
                                    <Text className="text-white/80 text-base">
                                        PDF dosyalarınızı yükleyin ve okuyun
                                    </Text>
                                </View>
                                <View className="bg-white/20 rounded-full p-3">
                                    <Text className="text-3xl">📖</Text>
                                </View>
                            </View>
                        </Card>

                        <Card
                            variant="gradient"
                            gradientColors={['#43e97b', '#38f9d7']}
                            onPress={() => navigation.navigate("Quiz" as never)}
                        >
                            <View className="flex-row items-center justify-between">
                                <View className="flex-1">
                                    <Text className="text-white font-bold text-xl mb-2">
                                        🧠 Quiz Çöz
                                    </Text>
                                    <Text className="text-white/80 text-base">
                                        Öğrendiğiniz kelimeleri test edin
                                    </Text>
                                </View>
                                <View className="bg-white/20 rounded-full p-3">
                                    <Text className="text-3xl">🧠</Text>
                                </View>
                            </View>
                        </Card>

                        <Card
                            variant="gradient"
                            gradientColors={['#fa709a', '#fee140']}
                            onPress={() => navigation.navigate("WordList" as never)}
                        >
                            <View className="flex-row items-center justify-between">
                                <View className="flex-1">
                                    <Text className="text-white font-bold text-xl mb-2">
                                        📝 Kelime Listem
                                    </Text>
                                    <Text className="text-white/80 text-base">
                                        Kaydettiğiniz kelimeleri görüntüleyin
                                    </Text>
                                </View>
                                <View className="bg-white/20 rounded-full p-3">
                                    <Text className="text-3xl">📝</Text>
                                </View>
                            </View>
                        </Card>
                    </View>
                </View>

                {/* Recent Activity */}
                <Card variant="elevated">
                    <Text className="text-xl font-bold text-gray-900 mb-4">
                        📈 Son Aktiviteler
                    </Text>
                    <View className="bg-gray-50 rounded-xl p-6 items-center">
                        <Text className="text-6xl mb-4">📚</Text>
                        <Text className="text-gray-600 text-center text-lg">
                            Henüz aktivite bulunmuyor
                        </Text>
                        <Text className="text-gray-500 text-center mt-2">
                            Okuma veya quiz yapmaya başlayın!
                        </Text>
                    </View>
                </Card>
            </View>
        </ScrollView>
    );
};

export default HomeScreen;
