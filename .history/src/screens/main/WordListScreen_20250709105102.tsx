import React from "react";
import { View, Text, TouchableOpacity, ScrollView, TextInput } from "react-native";

const WordListScreen: React.FC = () => {
    const sampleWords = [
        {
            word: "serendipity",
            meaning: "tesadüf",
            example: "It was pure serendipity that we met.",
        },
        {
            word: "ephemeral",
            meaning: "geçici",
            example: "The beauty of cherry blossoms is ephemeral.",
        },
        {
            word: "ubiquitous",
            meaning: "her yerde bulunan",
            example: "Smartphones are ubiquitous nowadays.",
        },
    ];

    return (
        <ScrollView className="flex-1 bg-white">
            <View className="px-6 pt-12 pb-32">
                <Text className="text-2xl font-bold text-gray-900 mb-6">Kelime Listem 📝</Text>

                {/* Search Bar */}
                <View className="mb-6">
                    <TextInput
                        className="border border-gray-300 rounded-lg px-4 py-3 text-gray-900"
                        placeholder="Kelime ara..."
                    />
                </View>

                {/* Stats */}
                <View className="bg-purple-50 rounded-xl p-6 mb-6">
                    <Text className="text-lg font-semibold text-purple-900 mb-4">
                        İstatistikler
                    </Text>
                    <View className="flex-row justify-between">
                        <View className="items-center">
                            <Text className="text-2xl font-bold text-purple-600">127</Text>
                            <Text className="text-purple-700 text-sm">Toplam Kelime</Text>
                        </View>
                        <View className="items-center">
                            <Text className="text-2xl font-bold text-purple-600">89</Text>
                            <Text className="text-purple-700 text-sm">Öğrenilen</Text>
                        </View>
                        <View className="items-center">
                            <Text className="text-2xl font-bold text-purple-600">38</Text>
                            <Text className="text-purple-700 text-sm">Öğreniliyor</Text>
                        </View>
                    </View>
                </View>

                {/* Word List */}
                <View>
                    <Text className="text-lg font-semibold text-gray-900 mb-4">Kelimelerim</Text>

                    <View className="space-y-3">
                        {sampleWords.map((item, index) => (
                            <View
                                key={index}
                                className="bg-white rounded-xl p-4 border border-gray-200 shadow-sm"
                            >
                                <View className="flex-row justify-between items-start mb-2">
                                    <Text className="text-lg font-semibold text-gray-900">
                                        {item.word}
                                    </Text>
                                    <View className="bg-green-100 px-2 py-1 rounded">
                                        <Text className="text-green-700 text-xs font-semibold">
                                            Öğrenildi
                                        </Text>
                                    </View>
                                </View>

                                <Text className="text-blue-600 font-medium mb-2">
                                    {item.meaning}
                                </Text>

                                <Text className="text-gray-600 text-sm italic">
                                    "{item.example}"
                                </Text>

                                <View className="flex-row justify-end mt-3 space-x-2">
                                    <TouchableOpacity className="bg-blue-500 px-3 py-1 rounded">
                                        <Text className="text-white text-sm">Quiz'e Ekle</Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity className="bg-red-500 px-3 py-1 rounded">
                                        <Text className="text-white text-sm">Sil</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        ))}
                    </View>
                </View>

                {/* Add Word Button */}
                <TouchableOpacity className="bg-purple-500 rounded-xl p-4 mt-6">
                    <Text className="text-white font-semibold text-center text-lg">
                        + Yeni Kelime Ekle
                    </Text>
                </TouchableOpacity>
            </View>
        </ScrollView>
    );
};

export default WordListScreen;
