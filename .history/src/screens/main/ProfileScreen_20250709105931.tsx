import React from "react";
import { View, Text, TouchableOpacity, ScrollView, StyleSheet } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { authService } from "../../services/auth";

const ProfileScreen: React.FC = () => {
    const handleLogout = async () => {
        try {
            await authService.signOut();
        } catch (error) {
            console.error("Logout error:", error);
        }
    };

    return (
        <LinearGradient colors={["#f8fafc", "#e2e8f0"]} style={styles.container}>
            <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                <View style={styles.content}>
                    {/* Header */}
                    <View style={styles.header}>
                        <Text style={styles.title}>Profilim 👤</Text>
                        <Text style={styles.subtitle}>
                            Hesap bilgileriniz ve öğrenme istatistikleriniz
                        </Text>
                    </View>

                    {/* User Info */}
                    <LinearGradient
                        colors={["#667eea", "#764ba2"]}
                        style={styles.userCard}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                    >
                        <View style={styles.userInfo}>
                            <View style={styles.avatarContainer}>
                                <LinearGradient
                                    colors={["#ffffff", "#f0f0f0"]}
                                    style={styles.avatar}
                                >
                                    <Text style={styles.avatarText}>U</Text>
                                </LinearGradient>
                            </View>
                            <Text style={styles.userName}>Kullanıcı Adı</Text>
                            <Text style={styles.userEmail}><EMAIL></Text>
                        </View>

                        <View style={styles.statsContainer}>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>127</Text>
                                <Text style={styles.statLabel}>Kelime</Text>
                            </View>
                            <View style={styles.statDivider} />
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>15</Text>
                                <Text style={styles.statLabel}>Quiz</Text>
                            </View>
                            <View style={styles.statDivider} />
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>7</Text>
                                <Text style={styles.statLabel}>Gün Streak</Text>
                            </View>
                        </View>
                    </LinearGradient>

                {/* Settings */}
                <View className="space-y-3 mb-6">
                    <Text className="text-lg font-semibold text-gray-900 mb-2">Ayarlar</Text>

                    <TouchableOpacity className="bg-white rounded-xl p-4 border border-gray-200 flex-row items-center justify-between">
                        <View className="flex-row items-center">
                            <Text className="text-lg mr-3">🔔</Text>
                            <Text className="text-gray-900 font-medium">Bildirimler</Text>
                        </View>
                        <Text className="text-gray-400">›</Text>
                    </TouchableOpacity>

                    <TouchableOpacity className="bg-white rounded-xl p-4 border border-gray-200 flex-row items-center justify-between">
                        <View className="flex-row items-center">
                            <Text className="text-lg mr-3">🌍</Text>
                            <Text className="text-gray-900 font-medium">Dil Ayarları</Text>
                        </View>
                        <Text className="text-gray-400">›</Text>
                    </TouchableOpacity>

                    <TouchableOpacity className="bg-white rounded-xl p-4 border border-gray-200 flex-row items-center justify-between">
                        <View className="flex-row items-center">
                            <Text className="text-lg mr-3">📊</Text>
                            <Text className="text-gray-900 font-medium">İstatistikler</Text>
                        </View>
                        <Text className="text-gray-400">›</Text>
                    </TouchableOpacity>

                    <TouchableOpacity className="bg-white rounded-xl p-4 border border-gray-200 flex-row items-center justify-between">
                        <View className="flex-row items-center">
                            <Text className="text-lg mr-3">💾</Text>
                            <Text className="text-gray-900 font-medium">Veri Yedekleme</Text>
                        </View>
                        <Text className="text-gray-400">›</Text>
                    </TouchableOpacity>

                    <TouchableOpacity className="bg-white rounded-xl p-4 border border-gray-200 flex-row items-center justify-between">
                        <View className="flex-row items-center">
                            <Text className="text-lg mr-3">❓</Text>
                            <Text className="text-gray-900 font-medium">Yardım & Destek</Text>
                        </View>
                        <Text className="text-gray-400">›</Text>
                    </TouchableOpacity>
                </View>

                {/* Learning Goals */}
                <View className="bg-green-50 rounded-xl p-6 mb-6">
                    <Text className="text-lg font-semibold text-green-900 mb-4">
                        Öğrenme Hedefleri
                    </Text>
                    <View className="space-y-3">
                        <View className="flex-row justify-between items-center">
                            <Text className="text-green-700">Günlük kelime hedefi</Text>
                            <Text className="text-green-900 font-semibold">10 kelime</Text>
                        </View>
                        <View className="flex-row justify-between items-center">
                            <Text className="text-green-700">Haftalık quiz hedefi</Text>
                            <Text className="text-green-900 font-semibold">5 quiz</Text>
                        </View>
                        <View className="flex-row justify-between items-center">
                            <Text className="text-green-700">Okuma süresi</Text>
                            <Text className="text-green-900 font-semibold">30 dakika</Text>
                        </View>
                    </View>
                </View>

                {/* Logout Button */}
                <TouchableOpacity className="bg-red-500 rounded-xl p-4" onPress={handleLogout}>
                    <Text className="text-white font-semibold text-center text-lg">Çıkış Yap</Text>
                </TouchableOpacity>
            </View>
        </ScrollView>
    );
};

export default ProfileScreen;
