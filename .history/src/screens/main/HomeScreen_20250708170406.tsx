import React from "react";
import { View, Text, ScrollView, StyleSheet, TouchableOpacity } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useNavigation } from "@react-navigation/native";
import { authService } from "../../services/auth";

const HomeScreen: React.FC = () => {
    const navigation = useNavigation();

    const handleLogout = async () => {
        try {
            await authService.signOut();
        } catch (error) {
            console.error("Logout error:", error);
        }
    };

    return (
        <LinearGradient colors={["#f8fafc", "#e2e8f0"]} style={styles.container}>
            <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                <View style={styles.content}>
                    {/* Header */}
                    <View style={styles.header}>
                        <View style={styles.headerText}>
                            <Text style={styles.title}>Hoş Geldiniz! 👋</Text>
                            <Text style={styles.subtitle}>
                                Di<PERSON> <PERSON>ğrenme yolculuğunuza devam edin
                            </Text>
                        </View>
                        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
                            <Text style={styles.logoutButtonText}>🚪 Çıkış</Text>
                        </TouchableOpacity>
                    </View>

                    {/* Quick Stats */}
                    <LinearGradient
                        colors={["#667eea", "#764ba2"]}
                        style={styles.statsCard}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                    >
                        <Text style={styles.statsTitle}>📊 Günlük İlerleme</Text>
                        <View style={styles.statsContainer}>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>12</Text>
                                <Text style={styles.statLabel}>Yeni Kelime</Text>
                            </View>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>3</Text>
                                <Text style={styles.statLabel}>Tamamlanan Quiz</Text>
                            </View>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>25</Text>
                                <Text style={styles.statLabel}>Dakika Okuma</Text>
                            </View>
                        </View>
                    </LinearGradient>

                {/* Quick Actions */}
                <View className="mb-8">
                    <Text className="text-xl font-bold text-gray-900 mb-6">
                        🚀 Hızlı Erişim
                    </Text>

                    <View className="space-y-4">
                        <Card
                            variant="gradient"
                            gradientColors={['#4facfe', '#00f2fe']}
                            onPress={() => navigation.navigate("Reading" as never)}
                        >
                            <View className="flex-row items-center justify-between">
                                <View className="flex-1">
                                    <Text className="text-white font-bold text-xl mb-2">
                                        📖 Okumaya Başla
                                    </Text>
                                    <Text className="text-white/80 text-base">
                                        PDF dosyalarınızı yükleyin ve okuyun
                                    </Text>
                                </View>
                                <View className="bg-white/20 rounded-full p-3">
                                    <Text className="text-3xl">📖</Text>
                                </View>
                            </View>
                        </Card>

                        <Card
                            variant="gradient"
                            gradientColors={['#43e97b', '#38f9d7']}
                            onPress={() => navigation.navigate("Quiz" as never)}
                        >
                            <View className="flex-row items-center justify-between">
                                <View className="flex-1">
                                    <Text className="text-white font-bold text-xl mb-2">
                                        🧠 Quiz Çöz
                                    </Text>
                                    <Text className="text-white/80 text-base">
                                        Öğrendiğiniz kelimeleri test edin
                                    </Text>
                                </View>
                                <View className="bg-white/20 rounded-full p-3">
                                    <Text className="text-3xl">🧠</Text>
                                </View>
                            </View>
                        </Card>

                        <Card
                            variant="gradient"
                            gradientColors={['#fa709a', '#fee140']}
                            onPress={() => navigation.navigate("WordList" as never)}
                        >
                            <View className="flex-row items-center justify-between">
                                <View className="flex-1">
                                    <Text className="text-white font-bold text-xl mb-2">
                                        📝 Kelime Listem
                                    </Text>
                                    <Text className="text-white/80 text-base">
                                        Kaydettiğiniz kelimeleri görüntüleyin
                                    </Text>
                                </View>
                                <View className="bg-white/20 rounded-full p-3">
                                    <Text className="text-3xl">📝</Text>
                                </View>
                            </View>
                        </Card>
                    </View>
                </View>

                {/* Recent Activity */}
                <Card variant="elevated">
                    <Text className="text-xl font-bold text-gray-900 mb-4">
                        📈 Son Aktiviteler
                    </Text>
                    <View className="bg-gray-50 rounded-xl p-6 items-center">
                        <Text className="text-6xl mb-4">📚</Text>
                        <Text className="text-gray-600 text-center text-lg">
                            Henüz aktivite bulunmuyor
                        </Text>
                        <Text className="text-gray-500 text-center mt-2">
                            Okuma veya quiz yapmaya başlayın!
                        </Text>
                    </View>
                </Card>
            </View>
        </ScrollView>
    );
};

export default HomeScreen;
