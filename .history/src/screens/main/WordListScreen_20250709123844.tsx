import React, { useState, useEffect } from "react";
import {
    View,
    Text,
    TouchableOpacity,
    ScrollView,
    TextInput,
    StyleSheet,
    Alert,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import AddWordModal, { WordData } from "../../components/ui/AddWordModal";
import { firestoreService, UserWord } from "../../services/firestore";
import { authService } from "../../services/auth";
import { onAuthStateChanged } from "firebase/auth";
import { auth } from "../../services/firebase";

const WordListScreen: React.FC = () => {
    const [searchText, setSearchText] = useState("");
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [words, setWords] = useState<UserWord[]>([]);
    const [loading, setLoading] = useState(true);

    // Load user words on component mount and listen to auth changes
    useEffect(() => {
        const unsubscribe = onAuthStateChanged(auth, (user) => {
            if (user) {
                console.log("Auth state changed - user logged in:", user.uid);
                loadUserWords();
            } else {
                console.log("Auth state changed - user logged out");
                setWords([]);
                setLoading(false);
            }
        });

        return () => unsubscribe();
    }, []);

    const loadUserWords = async () => {
        try {
            const currentUser = authService.getCurrentUser();
            console.log("Current user:", currentUser);

            if (!currentUser) {
                console.log("No authenticated user found");
                setLoading(false);
                return;
            }

            console.log("Loading words for user:", currentUser.uid);
            const userWords = await firestoreService.getUserWords(currentUser.uid);
            console.log("Loaded words:", userWords.length);
            setWords(userWords);
        } catch (error) {
            console.error("Error loading words:", error);
            Alert.alert(
                "Hata",
                "Kelimeler yüklenirken bir hata oluştu. Lütfen giriş yaptığınızdan emin olun."
            );
        } finally {
            setLoading(false);
        }
    };

    const handleAddWord = async (wordData: WordData) => {
        try {
            const currentUser = authService.getCurrentUser();
            if (!currentUser) {
                Alert.alert("Hata", "Lütfen giriş yapın");
                return;
            }

            setLoading(true);
            await firestoreService.addUserWordDirect(currentUser.uid, wordData);

            // Reload words to get the updated list
            await loadUserWords();

            Alert.alert("Başarılı", "Kelime başarıyla eklendi!");
        } catch (error) {
            console.error("Error adding word:", error);
            Alert.alert("Hata", "Kelime eklenirken bir hata oluştu");
        } finally {
            setLoading(false);
        }
    };

    const handleDeleteWord = async (wordId: string) => {
        Alert.alert("Kelimeyi Sil", "Bu kelimeyi silmek istediğinizden emin misiniz?", [
            { text: "İptal", style: "cancel" },
            {
                text: "Sil",
                style: "destructive",
                onPress: async () => {
                    try {
                        setLoading(true);
                        await firestoreService.deleteUserWord(wordId);
                        await loadUserWords();
                        Alert.alert("Başarılı", "Kelime silindi!");
                    } catch (error) {
                        console.error("Error deleting word:", error);
                        Alert.alert("Hata", "Kelime silinirken bir hata oluştu");
                    } finally {
                        setLoading(false);
                    }
                },
            },
        ]);
    };

    return (
        <LinearGradient colors={["#f8fafc", "#e2e8f0"]} style={styles.container}>
            <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                <View style={styles.content}>
                    {/* Header */}
                    <View style={styles.header}>
                        <Text style={styles.title}>Kelime Koleksiyonum 📝</Text>
                        <Text style={styles.subtitle}>
                            Öğrendiğiniz kelimeleri takip edin ve gelişiminizi izleyin
                        </Text>
                    </View>

                    {/* Search Bar */}
                    <View style={styles.searchContainer}>
                        <TextInput
                            style={styles.searchInput}
                            placeholder="Kelime ara..."
                            placeholderTextColor="#9CA3AF"
                            value={searchText}
                            onChangeText={setSearchText}
                        />
                        <View style={styles.searchIcon}>
                            <Text style={styles.searchEmoji}>🔍</Text>
                        </View>
                    </View>

                    {/* Stats */}
                    <LinearGradient
                        colors={["#667eea", "#764ba2"]}
                        style={styles.statsCard}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                    >
                        <Text style={styles.statsTitle}>📊 Kelime İstatistikleri</Text>
                        <View style={styles.statsContainer}>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>127</Text>
                                <Text style={styles.statLabel}>Toplam Kelime</Text>
                            </View>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>89</Text>
                                <Text style={styles.statLabel}>Öğrenilen</Text>
                            </View>
                            <View style={styles.statItem}>
                                <Text style={styles.statNumber}>38</Text>
                                <Text style={styles.statLabel}>Öğreniliyor</Text>
                            </View>
                        </View>
                    </LinearGradient>

                    {/* Word List */}
                    <View style={styles.section}>
                        <Text style={styles.sectionTitle}>📚 Kelimelerim</Text>

                        <View style={styles.wordsContainer}>
                            {loading && words.length === 0 ? (
                                <View style={styles.loadingContainer}>
                                    <Text style={styles.loadingText}>Kelimeler yükleniyor...</Text>
                                </View>
                            ) : words.length === 0 ? (
                                <View style={styles.emptyContainer}>
                                    <Text style={styles.emptyText}>Henüz kelime eklenmemiş</Text>
                                    <Text style={styles.emptySubtext}>
                                        İlk kelimenizi eklemek için yukarıdaki butona tıklayın
                                    </Text>
                                </View>
                            ) : (
                                words.map((item, index) => (
                                    <View key={index} style={styles.wordCard}>
                                        <View style={styles.wordHeader}>
                                            <View style={styles.wordTitleContainer}>
                                                <Text style={styles.wordTitle}>{item.word}</Text>
                                                <View style={styles.categoryBadge}>
                                                    <Text style={styles.categoryText}>
                                                        {item.partOfSpeech || "kelime"}
                                                    </Text>
                                                </View>
                                            </View>
                                            <View
                                                style={[
                                                    styles.levelBadge,
                                                    item.learned
                                                        ? styles.learnedBadge
                                                        : styles.learningBadge,
                                                ]}
                                            >
                                                <Text
                                                    style={[
                                                        styles.levelText,
                                                        item.learned
                                                            ? styles.learnedText
                                                            : styles.learningText,
                                                    ]}
                                                >
                                                    {item.learned
                                                        ? "✓ Öğrenildi"
                                                        : "📖 Öğreniliyor"}
                                                </Text>
                                            </View>
                                        </View>

                                        <Text style={styles.wordMeaning}>{item.meaning}</Text>

                                        <View style={styles.exampleContainer}>
                                            <Text style={styles.exampleLabel}>Örnek:</Text>
                                            <Text style={styles.exampleText}>
                                                "{item.examples[0] || "Örnek cümle yok"}"
                                            </Text>
                                        </View>

                                        <View style={styles.actionButtons}>
                                            <TouchableOpacity style={styles.quizButton}>
                                                <Text style={styles.quizButtonText}>
                                                    🧠 Quiz'e Ekle
                                                </Text>
                                            </TouchableOpacity>
                                            <TouchableOpacity
                                                style={styles.deleteButton}
                                                onPress={() => handleDeleteWord(item.id!)}
                                            >
                                                <Text style={styles.deleteButtonText}>🗑️</Text>
                                            </TouchableOpacity>
                                        </View>
                                    </View>
                                ))
                            )}
                        </View>
                    </View>

                    {/* Add Word Button */}
                    <LinearGradient
                        colors={["#fa709a", "#fee140"]}
                        style={styles.addButton}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                    >
                        <TouchableOpacity
                            style={styles.addButtonTouchable}
                            onPress={() => setIsModalVisible(true)}
                        >
                            <Text style={styles.addButtonIcon}>➕</Text>
                            <Text style={styles.addButtonText}>Yeni Kelime Ekle</Text>
                        </TouchableOpacity>
                    </LinearGradient>

                    {/* Add Word Modal */}
                    <AddWordModal
                        visible={isModalVisible}
                        onClose={() => setIsModalVisible(false)}
                        onAddWord={handleAddWord}
                    />
                </View>
            </ScrollView>
        </LinearGradient>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    scrollView: {
        flex: 1,
    },
    content: {
        paddingHorizontal: 24,
        paddingTop: 64,
        paddingBottom: 120,
    },
    header: {
        marginBottom: 24,
    },
    title: {
        fontSize: 28,
        fontWeight: "bold",
        color: "#1F2937",
        marginBottom: 8,
    },
    subtitle: {
        fontSize: 18,
        color: "#6B7280",
    },
    searchContainer: {
        position: "relative",
        marginBottom: 24,
    },
    searchInput: {
        backgroundColor: "white",
        borderRadius: 16,
        paddingHorizontal: 20,
        paddingVertical: 16,
        fontSize: 16,
        color: "#1F2937",
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 3,
        paddingRight: 50,
    },
    searchIcon: {
        position: "absolute",
        right: 16,
        top: 16,
        bottom: 16,
        justifyContent: "center",
    },
    searchEmoji: {
        fontSize: 20,
    },
    statsCard: {
        borderRadius: 20,
        padding: 24,
        marginBottom: 32,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 10 },
        shadowOpacity: 0.3,
        shadowRadius: 20,
        elevation: 10,
    },
    statsTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: "white",
        textAlign: "center",
        marginBottom: 24,
    },
    statsContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
    },
    statItem: {
        alignItems: "center",
    },
    statNumber: {
        fontSize: 28,
        fontWeight: "bold",
        color: "white",
        marginBottom: 4,
    },
    statLabel: {
        fontSize: 14,
        color: "rgba(255, 255, 255, 0.8)",
        fontWeight: "500",
        textAlign: "center",
    },
    section: {
        marginBottom: 32,
    },
    sectionTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: "#1F2937",
        marginBottom: 16,
    },
    wordsContainer: {
        gap: 16,
    },
    wordCard: {
        backgroundColor: "white",
        borderRadius: 16,
        padding: 20,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 12,
        elevation: 5,
    },
    wordHeader: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "flex-start",
        marginBottom: 12,
    },
    wordTitleContainer: {
        flex: 1,
        marginRight: 12,
    },
    wordTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: "#1F2937",
        marginBottom: 4,
    },
    categoryBadge: {
        backgroundColor: "#F3F4F6",
        borderRadius: 8,
        paddingHorizontal: 8,
        paddingVertical: 2,
        alignSelf: "flex-start",
    },
    categoryText: {
        fontSize: 12,
        color: "#6B7280",
        fontWeight: "500",
    },
    levelBadge: {
        borderRadius: 12,
        paddingHorizontal: 12,
        paddingVertical: 6,
    },
    learnedBadge: {
        backgroundColor: "#D1FAE5",
    },
    learningBadge: {
        backgroundColor: "#DBEAFE",
    },
    levelText: {
        fontSize: 12,
        fontWeight: "600",
    },
    learnedText: {
        color: "#065F46",
    },
    learningText: {
        color: "#1E40AF",
    },
    wordMeaning: {
        fontSize: 16,
        color: "#3B82F6",
        fontWeight: "600",
        marginBottom: 12,
    },
    exampleContainer: {
        backgroundColor: "#F9FAFB",
        borderRadius: 12,
        padding: 12,
        marginBottom: 16,
    },
    exampleLabel: {
        fontSize: 12,
        color: "#6B7280",
        fontWeight: "600",
        marginBottom: 4,
    },
    exampleText: {
        fontSize: 14,
        color: "#374151",
        fontStyle: "italic",
        lineHeight: 20,
    },
    actionButtons: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
    },
    quizButton: {
        backgroundColor: "#3B82F6",
        borderRadius: 8,
        paddingHorizontal: 16,
        paddingVertical: 8,
        flex: 1,
        marginRight: 8,
    },
    quizButtonText: {
        color: "white",
        fontSize: 14,
        fontWeight: "600",
        textAlign: "center",
    },
    deleteButton: {
        backgroundColor: "#FEE2E2",
        borderRadius: 8,
        padding: 8,
        width: 40,
        alignItems: "center",
    },
    deleteButtonText: {
        fontSize: 16,
    },
    addButton: {
        borderRadius: 20,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.2,
        shadowRadius: 16,
        elevation: 8,
    },
    addButtonTouchable: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        padding: 20,
    },
    addButtonIcon: {
        fontSize: 20,
        marginRight: 12,
    },
    addButtonText: {
        color: "white",
        fontSize: 18,
        fontWeight: "bold",
    },
    loadingContainer: {
        padding: 40,
        alignItems: "center",
    },
    loadingText: {
        fontSize: 16,
        color: "#6B7280",
        fontWeight: "500",
    },
    emptyContainer: {
        padding: 40,
        alignItems: "center",
    },
    emptyText: {
        fontSize: 18,
        color: "#374151",
        fontWeight: "600",
        marginBottom: 8,
        textAlign: "center",
    },
    emptySubtext: {
        fontSize: 14,
        color: "#6B7280",
        textAlign: "center",
        lineHeight: 20,
    },
});

export default WordListScreen;
