import React, { useState } from "react";
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    Alert,
    ScrollView,
    KeyboardAvoidingView,
    Platform,
    StyleSheet,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { authService } from "../../services/auth";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AuthStackParamList } from "../../types/navigation";

type RegisterScreenNavigationProp = NativeStackNavigationProp<AuthStackParamList, "Register">;

const RegisterScreen: React.FC = () => {
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState<{
        email?: string;
        password?: string;
        confirmPassword?: string;
    }>({});
    const navigation = useNavigation<RegisterScreenNavigationProp>();

    const validateForm = () => {
        const newErrors: { email?: string; password?: string; confirmPassword?: string } = {};

        if (!email) {
            newErrors.email = "E-posta adresi gerekli";
        } else if (!/\S+@\S+\.\S+/.test(email)) {
            newErrors.email = "Geçerli bir e-posta adresi girin";
        }

        if (!password) {
            newErrors.password = "Şifre gerekli";
        } else if (password.length < 6) {
            newErrors.password = "Şifre en az 6 karakter olmalı";
        }

        if (!confirmPassword) {
            newErrors.confirmPassword = "Şifre tekrarı gerekli";
        } else if (password !== confirmPassword) {
            newErrors.confirmPassword = "Şifreler eşleşmiyor";
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleRegister = async () => {
        if (!validateForm()) return;

        setLoading(true);
        try {
            await authService.signUp(email, password);
        } catch (error: any) {
            Alert.alert("Kayıt Hatası", error.message);
        } finally {
            setLoading(false);
        }
    };

    return (
        <LinearGradient colors={["#667eea", "#764ba2"]} style={styles.container}>
            <KeyboardAvoidingView
                behavior={Platform.OS === "ios" ? "padding" : "height"}
                style={styles.container}
            >
                <ScrollView
                    contentContainerStyle={styles.scrollContent}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={styles.content}>
                        {/* Header */}
                        <View style={styles.header}>
                            <View style={styles.iconContainer}>
                                <Text style={styles.icon}>📝</Text>
                            </View>
                            <Text style={styles.title}>Kayıt Ol</Text>
                            <Text style={styles.subtitle}>
                                Yeni hesap oluşturun ve öğrenmeye başlayın
                            </Text>
                        </View>

                        {/* Register Form */}
                        <View style={styles.formContainer}>
                            <Text style={styles.formTitle}>Hesap Oluştur</Text>

                            <View style={styles.inputContainer}>
                                <Text style={styles.label}>E-posta</Text>
                                <TextInput
                                    style={[styles.input, errors.email && styles.inputError]}
                                    placeholder="<EMAIL>"
                                    value={email}
                                    onChangeText={setEmail}
                                    keyboardType="email-address"
                                    autoCapitalize="none"
                                    placeholderTextColor="#9CA3AF"
                                />
                                {errors.email && (
                                    <Text style={styles.errorText}>{errors.email}</Text>
                                )}
                            </View>

                            <View style={styles.inputContainer}>
                                <Text style={styles.label}>Şifre</Text>
                                <TextInput
                                    style={[styles.input, errors.password && styles.inputError]}
                                    placeholder="En az 6 karakter"
                                    value={password}
                                    onChangeText={setPassword}
                                    secureTextEntry
                                    placeholderTextColor="#9CA3AF"
                                />
                                {errors.password && (
                                    <Text style={styles.errorText}>{errors.password}</Text>
                                )}
                            </View>

                            <View style={styles.inputContainer}>
                                <Text style={styles.label}>Şifre Tekrar</Text>
                                <TextInput
                                    style={[
                                        styles.input,
                                        errors.confirmPassword && styles.inputError,
                                    ]}
                                    placeholder="Şifrenizi tekrar girin"
                                    value={confirmPassword}
                                    onChangeText={setConfirmPassword}
                                    secureTextEntry
                                    placeholderTextColor="#9CA3AF"
                                />
                                {errors.confirmPassword && (
                                    <Text style={styles.errorText}>{errors.confirmPassword}</Text>
                                )}
                            </View>

                            <TouchableOpacity
                                style={[styles.button, loading && styles.buttonDisabled]}
                                onPress={handleRegister}
                                disabled={loading}
                            >
                                <Text style={styles.buttonText}>
                                    {loading ? "Kayıt yapılıyor..." : "Kayıt Ol"}
                                </Text>
                            </TouchableOpacity>
                        </View>

                        {/* Login Link */}
                        <View style={styles.loginContainer}>
                            <Text style={styles.loginText}>Zaten hesabınız var mı? </Text>
                            <TouchableOpacity onPress={() => navigation.navigate("Login")}>
                                <Text style={styles.loginLink}>Giriş Yap</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </ScrollView>
            </KeyboardAvoidingView>
        </LinearGradient>
    );
};

export default RegisterScreen;
