import React, { useState } from "react";
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    Alert,
    ScrollView,
    KeyboardAvoidingView,
    Platform,
    StyleSheet,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { sendPasswordResetEmail } from "firebase/auth";
import { auth } from "../../services/firebase";
import { useNavigation } from "@react-navigation/native";

const ForgotPasswordScreen: React.FC = () => {
    const [email, setEmail] = useState("");
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState<{ email?: string }>({});
    const navigation = useNavigation();

    const validateForm = () => {
        const newErrors: { email?: string } = {};

        if (!email) {
            newErrors.email = "E-posta adresi gerekli";
        } else if (!/\S+@\S+\.\S+/.test(email)) {
            newErrors.email = "Geçerli bir e-posta adresi girin";
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleResetPassword = async () => {
        if (!validateForm()) return;

        setLoading(true);
        try {
            await sendPasswordResetEmail(auth, email);
            Alert.alert("Başarılı", "Şifre sıfırlama bağlantısı e-posta adresinize gönderildi", [
                { text: "Tamam", onPress: () => navigation.goBack() },
            ]);
        } catch (error: any) {
            Alert.alert("Hata", error.message);
        } finally {
            setLoading(false);
        }
    };

    return (
        <LinearGradient colors={["#667eea", "#764ba2"]} style={styles.container}>
            <KeyboardAvoidingView
                behavior={Platform.OS === "ios" ? "padding" : "height"}
                style={styles.container}
            >
                <ScrollView
                    contentContainerStyle={styles.scrollContent}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={styles.content}>
                        {/* Header */}
                        <View style={styles.header}>
                            <View style={styles.iconContainer}>
                                <Text style={styles.icon}>🔑</Text>
                            </View>
                            <Text style={styles.title}>Şifremi Unuttum</Text>
                            <Text style={styles.subtitle}>
                                E-posta adresinize şifre sıfırlama bağlantısı göndereceğiz
                            </Text>
                        </View>

                        {/* Reset Form */}
                        <View style={styles.formContainer}>
                            <Text style={styles.formTitle}>Şifre Sıfırlama</Text>

                            <View style={styles.inputContainer}>
                                <Text style={styles.label}>E-posta</Text>
                                <TextInput
                                    style={[styles.input, errors.email && styles.inputError]}
                                    placeholder="<EMAIL>"
                                    value={email}
                                    onChangeText={setEmail}
                                    keyboardType="email-address"
                                    autoCapitalize="none"
                                    placeholderTextColor="#9CA3AF"
                                />
                                {errors.email && (
                                    <Text style={styles.errorText}>{errors.email}</Text>
                                )}
                            </View>

                            <TouchableOpacity
                                style={[styles.button, loading && styles.buttonDisabled]}
                                onPress={handleResetPassword}
                                disabled={loading}
                            >
                                <Text style={styles.buttonText}>
                                    {loading
                                        ? "Gönderiliyor..."
                                        : "Şifre Sıfırlama Bağlantısı Gönder"}
                                </Text>
                            </TouchableOpacity>

                            <TouchableOpacity
                                style={styles.backButton}
                                onPress={() => navigation.goBack()}
                            >
                                <Text style={styles.backButtonText}>← Geri Dön</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </ScrollView>
            </KeyboardAvoidingView>
        </LinearGradient>
    );
};

export default ForgotPasswordScreen;
