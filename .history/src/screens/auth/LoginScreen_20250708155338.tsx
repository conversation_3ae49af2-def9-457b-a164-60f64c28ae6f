import React, { useState } from "react";
import { View, Text, Alert, ScrollView, KeyboardAvoidingView, Platform } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { authService } from "../../services/auth";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AuthStackParamList } from "../../types/navigation";
import Button from "../../components/ui/Button";
import Input from "../../components/ui/Input";
import Card from "../../components/ui/Card";

type LoginScreenNavigationProp = NativeStackNavigationProp<AuthStackParamList, "Login">;

const LoginScreen: React.FC = () => {
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState<{ email?: string; password?: string }>({});
    const navigation = useNavigation<LoginScreenNavigationProp>();

    const validateForm = () => {
        const newErrors: { email?: string; password?: string } = {};

        if (!email) {
            newErrors.email = "E-posta adresi gerekli";
        } else if (!/\S+@\S+\.\S+/.test(email)) {
            newErrors.email = "Geçerli bir e-posta adresi girin";
        }

        if (!password) {
            newErrors.password = "Şifre gerekli";
        } else if (password.length < 6) {
            newErrors.password = "Şifre en az 6 karakter olmalı";
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleLogin = async () => {
        if (!validateForm()) return;

        setLoading(true);
        try {
            await authService.signIn(email, password);
        } catch (error: any) {
            Alert.alert("Giriş Hatası", error.message);
        } finally {
            setLoading(false);
        }
    };

    return (
        <View className="flex-1 bg-white px-6 justify-center">
            <View className="mb-8">
                <Text className="text-3xl font-bold text-gray-900 text-center mb-2">
                    LinguaRead
                </Text>
                <Text className="text-gray-600 text-center">Hesabınıza giriş yapın</Text>
            </View>

            <View className="space-y-4">
                <View>
                    <Text className="text-gray-700 mb-2">E-posta</Text>
                    <TextInput
                        className="border border-gray-300 rounded-lg px-4 py-3 text-gray-900"
                        placeholder="<EMAIL>"
                        value={email}
                        onChangeText={setEmail}
                        keyboardType="email-address"
                        autoCapitalize="none"
                    />
                </View>

                <View>
                    <Text className="text-gray-700 mb-2">Şifre</Text>
                    <TextInput
                        className="border border-gray-300 rounded-lg px-4 py-3 text-gray-900"
                        placeholder="Şifrenizi girin"
                        value={password}
                        onChangeText={setPassword}
                        secureTextEntry
                    />
                </View>

                <TouchableOpacity
                    className={`bg-primary-500 rounded-lg py-3 ${loading ? "opacity-50" : ""}`}
                    onPress={handleLogin}
                    disabled={loading}
                >
                    <Text className="text-white text-center font-semibold text-lg">
                        {loading ? "Giriş yapılıyor..." : "Giriş Yap"}
                    </Text>
                </TouchableOpacity>

                <View className="flex-row justify-center space-x-1 mt-4">
                    <Text className="text-gray-600">Hesabınız yok mu?</Text>
                    <TouchableOpacity onPress={() => navigation.navigate("Register")}>
                        <Text className="text-primary-500 font-semibold">Kayıt Ol</Text>
                    </TouchableOpacity>
                </View>

                <TouchableOpacity
                    className="mt-2"
                    onPress={() => navigation.navigate("ForgotPassword")}
                >
                    <Text className="text-primary-500 text-center">Şifremi Unuttum</Text>
                </TouchableOpacity>
            </View>
        </View>
    );
};

export default LoginScreen;
