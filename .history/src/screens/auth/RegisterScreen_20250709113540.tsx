import React, { useState } from "react";
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    Alert,
    ScrollView,
    KeyboardAvoidingView,
    Platform,
    StyleSheet,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { authService } from "../../services/auth";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AuthStackParamList } from "../../types/navigation";

type RegisterScreenNavigationProp = NativeStackNavigationProp<AuthStackParamList, "Register">;

const RegisterScreen: React.FC = () => {
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState<{
        email?: string;
        password?: string;
        confirmPassword?: string;
    }>({});
    const navigation = useNavigation<RegisterScreenNavigationProp>();

    const validateForm = () => {
        const newErrors: { email?: string; password?: string; confirmPassword?: string } = {};

        if (!email) {
            newErrors.email = "E-posta adresi gerekli";
        } else if (!/\S+@\S+\.\S+/.test(email)) {
            newErrors.email = "Geçerli bir e-posta adresi girin";
        }

        if (!password) {
            newErrors.password = "Şifre gerekli";
        } else if (password.length < 6) {
            newErrors.password = "Şifre en az 6 karakter olmalı";
        }

        if (!confirmPassword) {
            newErrors.confirmPassword = "Şifre tekrarı gerekli";
        } else if (password !== confirmPassword) {
            newErrors.confirmPassword = "Şifreler eşleşmiyor";
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleRegister = async () => {
        if (!validateForm()) return;

        setLoading(true);
        try {
            await authService.signUp(email, password);
        } catch (error: any) {
            Alert.alert("Kayıt Hatası", error.message);
        } finally {
            setLoading(false);
        }
    };

    return (
        <View className="flex-1 bg-white px-6 justify-center">
            <View className="mb-8">
                <Text className="text-3xl font-bold text-gray-900 text-center mb-2">Kayıt Ol</Text>
                <Text className="text-gray-600 text-center">Yeni hesap oluşturun</Text>
            </View>

            <View className="space-y-4">
                <View>
                    <Text className="text-gray-700 mb-2">E-posta</Text>
                    <TextInput
                        className="border border-gray-300 rounded-lg px-4 py-3 text-gray-900"
                        placeholder="<EMAIL>"
                        value={email}
                        onChangeText={setEmail}
                        keyboardType="email-address"
                        autoCapitalize="none"
                    />
                </View>

                <View>
                    <Text className="text-gray-700 mb-2">Şifre</Text>
                    <TextInput
                        className="border border-gray-300 rounded-lg px-4 py-3 text-gray-900"
                        placeholder="En az 6 karakter"
                        value={password}
                        onChangeText={setPassword}
                        secureTextEntry
                    />
                </View>

                <View>
                    <Text className="text-gray-700 mb-2">Şifre Tekrar</Text>
                    <TextInput
                        className="border border-gray-300 rounded-lg px-4 py-3 text-gray-900"
                        placeholder="Şifrenizi tekrar girin"
                        value={confirmPassword}
                        onChangeText={setConfirmPassword}
                        secureTextEntry
                    />
                </View>

                <TouchableOpacity
                    className={`bg-primary-500 rounded-lg py-3 ${loading ? "opacity-50" : ""}`}
                    onPress={handleRegister}
                    disabled={loading}
                >
                    <Text className="text-white text-center font-semibold text-lg">
                        {loading ? "Kayıt yapılıyor..." : "Kayıt Ol"}
                    </Text>
                </TouchableOpacity>

                <View className="flex-row justify-center space-x-1 mt-4">
                    <Text className="text-gray-600">Zaten hesabınız var mı?</Text>
                    <TouchableOpacity onPress={() => navigation.navigate("Login")}>
                        <Text className="text-primary-500 font-semibold">Giriş Yap</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
};

export default RegisterScreen;
