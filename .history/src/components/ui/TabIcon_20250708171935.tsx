import React from "react";
import { View, Text, StyleSheet } from "react-native";

interface TabIconProps {
    name: string;
    emoji: string;
    focused: boolean;
    color?: string;
}

const TabIcon: React.FC<TabIconProps> = ({ name, emoji, focused, color = "#3b82f6" }) => {
    return (
        <View style={styles.container}>
            <View
                style={[
                    styles.iconContainer,
                    focused ? styles.iconContainerFocused : styles.iconContainerDefault,
                ]}
            >
                <Text style={[styles.emoji, focused ? styles.emojiFocused : styles.emojiDefault]}>
                    {emoji}
                </Text>
            </View>

            <Text style={[styles.label, focused ? styles.labelFocused : styles.labelDefault]}>
                {name}
            </Text>

            {focused && <View style={styles.indicator} />}
        </View>
    );
};

export default TabIcon;
