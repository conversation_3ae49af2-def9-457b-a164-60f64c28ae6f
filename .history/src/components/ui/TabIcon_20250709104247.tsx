import React from "react";
import { View, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface TabIconProps {
    iconName: keyof typeof Ionicons.glyphMap;
    focused: boolean;
    size?: number;
}

const TabIcon: React.FC<TabIconProps> = ({ iconName, focused, size = 24 }) => {
    return (
        <View style={styles.container}>
            <View
                style={[
                    styles.iconContainer,
                    focused ? styles.iconContainerFocused : styles.iconContainerDefault,
                ]}
            >
                <Ionicons
                    name={iconName}
                    size={size}
                    color={focused ? "#ffffff" : "#64748b"}
                    style={focused ? styles.iconFocused : styles.iconDefault}
                />
            </View>
            {focused && <View style={styles.indicator} />}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        alignItems: "center",
        justifyContent: "center",
        paddingVertical: 8,
    },
    iconContainer: {
        width: 56,
        height: 56,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 28,
        marginBottom: 4,
    },
    iconContainerFocused: {
        backgroundColor: "#3b82f6",
        shadowColor: "#3b82f6",
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 8,
    },
    iconContainerDefault: {
        backgroundColor: "#f8fafc",
        borderWidth: 1,
        borderColor: "#e2e8f0",
    },
    iconFocused: {
        transform: [{ scale: 1.1 }],
    },
    iconDefault: {
        transform: [{ scale: 1.0 }],
    },
    indicator: {
        width: 6,
        height: 6,
        backgroundColor: "#3b82f6",
        borderRadius: 3,
        marginTop: 2,
    },
});

export default TabIcon;
