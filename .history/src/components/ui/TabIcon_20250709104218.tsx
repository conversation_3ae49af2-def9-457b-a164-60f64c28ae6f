import React from "react";
import { View, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface TabIconProps {
    iconName: keyof typeof Ionicons.glyphMap;
    focused: boolean;
    size?: number;
}

const TabIcon: React.FC<TabIconProps> = ({ name, emoji, focused, color = "#3b82f6" }) => {
    return (
        <View style={styles.container}>
            <View
                style={[
                    styles.iconContainer,
                    focused ? styles.iconContainerFocused : styles.iconContainerDefault,
                ]}
            >
                <Text style={[styles.emoji, focused ? styles.emojiFocused : styles.emojiDefault]}>
                    {emoji}
                </Text>
            </View>

            <Text style={[styles.label, focused ? styles.labelFocused : styles.labelDefault]}>
                {name}
            </Text>

            {focused && <View style={styles.indicator} />}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        alignItems: "center",
        justifyContent: "center",
    },
    iconContainer: {
        width: 48,
        height: 32,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 16,
    },
    iconContainerFocused: {
        backgroundColor: "#dbeafe",
    },
    iconContainerDefault: {
        backgroundColor: "transparent",
    },
    emoji: {
        fontSize: 20,
    },
    emojiFocused: {
        transform: [{ scale: 1.1 }],
    },
    emojiDefault: {
        transform: [{ scale: 1.0 }],
    },
    label: {
        fontSize: 12,
        fontWeight: "500",
        marginTop: 4,
    },
    labelFocused: {
        color: "#2563eb",
    },
    labelDefault: {
        color: "#6b7280",
    },
    indicator: {
        width: 4,
        height: 4,
        backgroundColor: "#2563eb",
        borderRadius: 2,
        marginTop: 4,
    },
});

export default TabIcon;
