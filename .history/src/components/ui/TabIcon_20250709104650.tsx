import React from "react";
import { View, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface TabIconProps {
    iconName: keyof typeof Ionicons.glyphMap;
    focused: boolean;
    size?: number;
}

const TabIcon: React.FC<TabIconProps> = ({ iconName, focused, size = 24 }) => {
    return (
        <View style={styles.container}>
            <View
                style={[
                    styles.iconContainer,
                    focused ? styles.iconContainerFocused : styles.iconContainerDefault,
                ]}
            >
                <Ionicons
                    name={iconName}
                    size={size}
                    color={focused ? "#ffffff" : "#64748b"}
                    style={focused ? styles.iconFocused : styles.iconDefault}
                />
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        alignItems: "center",
        justifyContent: "center",
        flex: 1,
    },
    iconContainer: {
        width: 56,
        height: 56,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 28,
    },
    iconContainerFocused: {
        backgroundColor: "#3b82f6",
        shadowColor: "#3b82f6",
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 8,
    },
    iconContainerDefault: {
        backgroundColor: "#f8fafc",
        borderWidth: 1,
        borderColor: "#e2e8f0",
    },
    iconFocused: {
        transform: [{ scale: 1.1 }],
    },
    iconDefault: {
        transform: [{ scale: 1.0 }],
    },
});

export default TabIcon;
