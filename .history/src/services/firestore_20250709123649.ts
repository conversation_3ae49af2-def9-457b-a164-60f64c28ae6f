import {
    collection,
    doc,
    addDoc,
    updateDoc,
    deleteDoc,
    getDocs,
    query,
    where,
    orderBy,
    limit,
    Timestamp,
} from "firebase/firestore";
import { db } from "./firebase";
import { TranslationResult } from "./translation";

// Data types
export interface UserWord {
    id?: string;
    userId: string;
    word: string;
    meaning: string;
    pronunciation?: string;
    partOfSpeech?: string;
    examples: string[];
    synonyms?: string[];
    difficulty?: "beginner" | "intermediate" | "advanced";
    learned: boolean;
    createdAt: Timestamp;
    lastReviewed?: Timestamp;
    reviewCount: number;
}

export interface QuizResult {
    id?: string;
    userId: string;
    score: number;
    totalQuestions: number;
    quizType: string;
    completedAt: Timestamp;
    timeSpent: number; // in seconds
    wordsQuizzed: string[];
}

export interface ReadingSession {
    id?: string;
    userId: string;
    documentName: string;
    documentId: string;
    startTime: Timestamp;
    endTime?: Timestamp;
    wordsLearned: string[];
    progress: number; // percentage
}

// Firestore service class
export class FirestoreService {
    // User Words
    async addUserWord(userId: string, translationResult: TranslationResult): Promise<string> {
        const userWord: Omit<UserWord, "id"> = {
            userId,
            word: translationResult.word,
            meaning: translationResult.meaning,
            pronunciation: translationResult.pronunciation,
            partOfSpeech: translationResult.partOfSpeech,
            examples: translationResult.examples,
            synonyms: translationResult.synonyms,
            difficulty: translationResult.difficulty,
            learned: false,
            createdAt: Timestamp.now(),
            reviewCount: 0,
        };

        const docRef = await addDoc(collection(db, "userWords"), userWord);
        return docRef.id;
    }

    // Add word directly from modal form
    async addUserWordDirect(
        userId: string,
        wordData: {
            word: string;
            meaning: string;
            pronunciation?: string;
            partOfSpeech: string;
            examples: string[];
            synonyms: string[];
            difficulty: "beginner" | "intermediate" | "advanced";
        }
    ): Promise<string> {
        const userWord: Omit<UserWord, "id"> = {
            userId,
            word: wordData.word,
            meaning: wordData.meaning,
            pronunciation: wordData.pronunciation,
            partOfSpeech: wordData.partOfSpeech,
            examples: wordData.examples,
            synonyms: wordData.synonyms,
            difficulty: wordData.difficulty,
            learned: false,
            createdAt: Timestamp.now(),
            reviewCount: 0,
        };

        const docRef = await addDoc(collection(db, "userWords"), userWord);
        return docRef.id;
    }

    async getUserWords(userId: string): Promise<UserWord[]> {
        log("getUserWords", userId);
        const q = query(
            collection(db, "userWords"),
            where("userId", "==", userId),
            orderBy("createdAt", "desc")
        );

        const querySnapshot = await getDocs(q);
        return querySnapshot.docs.map(
            (doc) =>
                ({
                    id: doc.id,
                    ...doc.data(),
                } as UserWord)
        );
    }

    async updateUserWord(wordId: string, updates: Partial<UserWord>): Promise<void> {
        const wordRef = doc(db, "userWords", wordId);
        await updateDoc(wordRef, updates);
    }

    async deleteUserWord(wordId: string): Promise<void> {
        await deleteDoc(doc(db, "userWords", wordId));
    }

    async markWordAsLearned(wordId: string): Promise<void> {
        await this.updateUserWord(wordId, {
            learned: true,
            lastReviewed: Timestamp.now(),
        });
    }

    // Quiz Results
    async saveQuizResult(quizResult: Omit<QuizResult, "id">): Promise<string> {
        const docRef = await addDoc(collection(db, "quizResults"), quizResult);
        return docRef.id;
    }

    async getUserQuizResults(userId: string, limitCount = 10): Promise<QuizResult[]> {
        const q = query(
            collection(db, "quizResults"),
            where("userId", "==", userId),
            orderBy("completedAt", "desc"),
            limit(limitCount)
        );

        const querySnapshot = await getDocs(q);
        return querySnapshot.docs.map(
            (doc) =>
                ({
                    id: doc.id,
                    ...doc.data(),
                } as QuizResult)
        );
    }

    // Reading Sessions
    async startReadingSession(
        userId: string,
        documentName: string,
        documentId: string
    ): Promise<string> {
        const session: Omit<ReadingSession, "id"> = {
            userId,
            documentName,
            documentId,
            startTime: Timestamp.now(),
            wordsLearned: [],
            progress: 0,
        };

        const docRef = await addDoc(collection(db, "readingSessions"), session);
        return docRef.id;
    }

    async updateReadingSession(sessionId: string, updates: Partial<ReadingSession>): Promise<void> {
        const sessionRef = doc(db, "readingSessions", sessionId);
        await updateDoc(sessionRef, updates);
    }

    async endReadingSession(
        sessionId: string,
        wordsLearned: string[],
        progress: number
    ): Promise<void> {
        await this.updateReadingSession(sessionId, {
            endTime: Timestamp.now(),
            wordsLearned,
            progress,
        });
    }

    async getUserReadingSessions(userId: string): Promise<ReadingSession[]> {
        const q = query(
            collection(db, "readingSessions"),
            where("userId", "==", userId),
            orderBy("startTime", "desc")
        );

        const querySnapshot = await getDocs(q);
        return querySnapshot.docs.map(
            (doc) =>
                ({
                    id: doc.id,
                    ...doc.data(),
                } as ReadingSession)
        );
    }

    // Statistics
    async getUserStats(userId: string) {
        const [words, quizResults, readingSessions] = await Promise.all([
            this.getUserWords(userId),
            this.getUserQuizResults(userId, 100),
            this.getUserReadingSessions(userId),
        ]);

        const totalWords = words.length;
        const learnedWords = words.filter((word) => word.learned).length;
        const totalQuizzes = quizResults.length;
        const averageScore =
            quizResults.length > 0
                ? (quizResults.reduce(
                      (sum, result) => sum + result.score / result.totalQuestions,
                      0
                  ) /
                      quizResults.length) *
                  100
                : 0;

        const totalReadingTime = readingSessions
            .filter((session) => session.endTime)
            .reduce((total, session) => {
                const duration = session.endTime!.seconds - session.startTime.seconds;
                return total + duration;
            }, 0);

        return {
            totalWords,
            learnedWords,
            learningWords: totalWords - learnedWords,
            totalQuizzes,
            averageScore: Math.round(averageScore),
            totalReadingTime: Math.round(totalReadingTime / 60), // in minutes
            currentStreak: this.calculateStreak(quizResults),
        };
    }

    private calculateStreak(quizResults: QuizResult[]): number {
        if (quizResults.length === 0) return 0;

        let streak = 0;
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const sortedResults = quizResults.sort(
            (a, b) => b.completedAt.seconds - a.completedAt.seconds
        );

        for (const result of sortedResults) {
            const resultDate = new Date(result.completedAt.seconds * 1000);
            resultDate.setHours(0, 0, 0, 0);

            const daysDiff = Math.floor(
                (today.getTime() - resultDate.getTime()) / (1000 * 60 * 60 * 24)
            );

            if (daysDiff === streak) {
                streak++;
            } else {
                break;
            }
        }

        return streak;
    }
}

// Export service instance
export const firestoreService = new FirestoreService();
