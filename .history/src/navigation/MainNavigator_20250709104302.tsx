import React from "react";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { MainTabParamList } from "../types/navigation";
import HomeScreen from "../screens/main/HomeScreen";
import ReadingScreen from "../screens/main/ReadingScreen";
import WordListScreen from "../screens/main/WordListScreen";
import QuizScreen from "../screens/main/QuizScreen";
import ProfileScreen from "../screens/main/ProfileScreen";
import TabIcon from "../components/ui/TabIcon";

const Tab = createBottomTabNavigator<MainTabParamList>();

const MainNavigator: React.FC = () => {
    return (
        <Tab.Navigator
            screenOptions={{
                headerShown: false,
                tabBarStyle: {
                    backgroundColor: "white",
                    borderTopWidth: 0,
                    elevation: 25,
                    shadowColor: "#000",
                    shadowOffset: { width: 0, height: -8 },
                    shadowOpacity: 0.12,
                    shadowRadius: 24,
                    paddingBottom: 20,
                    paddingTop: 16,
                    height: 100,
                    borderTopLeftRadius: 24,
                    borderTopRightRadius: 24,
                    position: "absolute",
                },
                tabBarShowLabel: false,
                tabBarActiveTintColor: "#3b82f6",
                tabBarInactiveTintColor: "#64748b",
            }}
        >
            <Tab.Screen
                name="Home"
                component={HomeScreen}
                options={{
                    tabBarIcon: ({ focused }) => (
                        <TabIcon name="Ana Sayfa" emoji="🏠" focused={focused} />
                    ),
                }}
            />
            <Tab.Screen
                name="Reading"
                component={ReadingScreen}
                options={{
                    tabBarIcon: ({ focused }) => (
                        <TabIcon name="Okuma" emoji="📖" focused={focused} />
                    ),
                }}
            />
            <Tab.Screen
                name="WordList"
                component={WordListScreen}
                options={{
                    tabBarIcon: ({ focused }) => (
                        <TabIcon name="Kelimeler" emoji="📝" focused={focused} />
                    ),
                }}
            />
            <Tab.Screen
                name="Quiz"
                component={QuizScreen}
                options={{
                    tabBarIcon: ({ focused }) => (
                        <TabIcon name="Quiz" emoji="🧠" focused={focused} />
                    ),
                }}
            />
            <Tab.Screen
                name="Profile"
                component={ProfileScreen}
                options={{
                    tabBarIcon: ({ focused }) => (
                        <TabIcon name="Profil" emoji="👤" focused={focused} />
                    ),
                }}
            />
        </Tab.Navigator>
    );
};

export default MainNavigator;
