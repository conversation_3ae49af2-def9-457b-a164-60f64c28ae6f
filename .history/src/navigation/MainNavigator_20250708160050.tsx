import React from "react";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { MainTabParamList } from "../types/navigation";
import HomeScreen from "../screens/main/HomeScreen";
import ReadingScreen from "../screens/main/ReadingScreen";
import WordListScreen from "../screens/main/WordListScreen";
import QuizScreen from "../screens/main/QuizScreen";
import ProfileScreen from "../screens/main/ProfileScreen";
import TabIcon from "../components/ui/TabIcon";

const Tab = createBottomTabNavigator<MainTabParamList>();

const MainNavigator: React.FC = () => {
    return (
        <Tab.Navigator
            screenOptions={{
                headerShown: false,
                tabBarStyle: {
                    backgroundColor: "white",
                    borderTopWidth: 1,
                    borderTopColor: "#e5e7eb",
                    paddingBottom: 8,
                    paddingTop: 8,
                    height: 60,
                },
                tabBarActiveTintColor: "#3b82f6",
                tabBarInactiveTintColor: "#6b7280",
                tabBarLabelStyle: {
                    fontSize: 12,
                    fontWeight: "500",
                },
            }}
        >
            <Tab.Screen
                name="Home"
                component={HomeScreen}
                options={{
                    tabBarLabel: "Ana Sayfa",
                    tabBarIcon: ({ focused }) => <TabIcon name="Home" focused={focused} />,
                }}
            />
            <Tab.Screen
                name="Reading"
                component={ReadingScreen}
                options={{
                    tabBarLabel: "Okuma",
                    tabBarIcon: ({ focused }) => <TabIcon name="Read" focused={focused} />,
                }}
            />
            <Tab.Screen
                name="WordList"
                component={WordListScreen}
                options={{
                    tabBarLabel: "Kelimeler",
                    tabBarIcon: ({ focused }) => <TabIcon name="Word" focused={focused} />,
                }}
            />
            <Tab.Screen
                name="Quiz"
                component={QuizScreen}
                options={{
                    tabBarLabel: "Quiz",
                    tabBarIcon: ({ focused }) => <TabIcon name="Quiz" focused={focused} />,
                }}
            />
            <Tab.Screen
                name="Profile"
                component={ProfileScreen}
                options={{
                    tabBarLabel: "Profil",
                    tabBarIcon: ({ focused }) => <TabIcon name="Prof" focused={focused} />,
                }}
            />
        </Tab.Navigator>
    );
};

export default MainNavigator;
